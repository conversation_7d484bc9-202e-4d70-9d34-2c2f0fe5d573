<template>
  <header class="dashboard-header">
    <div class="header-content">
      <div class="title-section">
        <h1>Debug Reports Dashboard</h1>
        <p>Manage and review client-side debug reports</p>
      </div>
      <div class="header-actions">
        <button @click="$emit('refresh')" :disabled="loading" class="btn btn-primary">
          {{ loading ? 'Refreshing...' : 'Refresh' }}
        </button>
        <button @click="$emit('toggle-theme')" class="btn btn-secondary">
          Toggle Theme
        </button>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
defineOptions({
  name: 'DebugDashboardHeader'
});

defineProps<{
  loading: boolean;
}>();

defineEmits(['refresh', 'toggle-theme']);
</script>

<style scoped>
.dashboard-header {
  background-color: var(--bg-surface, #fff);
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-base, #e0e0e0);
  box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0,0,0,0.1));
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1440px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

.title-section h1 {
  margin: 0 0 0.25rem 0;
  font-size: var(--font-size-2xl, 1.5rem);
  color: var(--text-primary, #333);
}

.title-section p {
  margin: 0;
  font-size: var(--font-size-sm, 0.875rem);
  color: var(--text-secondary, #666);
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md, 6px);
  border: 1px solid transparent;
  cursor: pointer;
  font-weight: var(--font-weight-semibold, 600);
  font-size: var(--font-size-sm, 0.875rem);
  transition: all 0.2s ease-in-out;
}

.btn:focus {
  outline: 2px solid var(--primary-500, #3b82f6);
  outline-offset: 2px;
}

.btn-primary {
  background-color: var(--primary-500, #3b82f6);
  color: white;
  border-color: var(--primary-500, #3b82f6);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-600, #2563eb);
  border-color: var(--primary-600, #2563eb);
}

.btn-primary:disabled {
  background-color: var(--primary-300, #93c5fd);
  border-color: var(--primary-300, #93c5fd);
  cursor: not-allowed;
}

.btn-secondary {
  background-color: var(--gray-200, #e2e8f0);
  color: var(--text-primary, #333);
  border-color: var(--gray-300, #cbd5e1);
}

.btn-secondary:hover {
  background-color: var(--gray-300, #cbd5e1);
  border-color: var(--gray-400, #94a3b8);
}
</style>