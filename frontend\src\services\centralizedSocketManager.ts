// Centralized Socket Manager
// This service handles all socket communication for the entire application
// to prevent multiple socket instances and ensure reliable event handling

import { io, type Socket } from 'socket.io-client';
import { useAuthStore } from '@/stores/auth';
import { useConnectionStore, type TransportType } from '@/stores/connection';
import { 
  INTEREST_RECEIVED, 
  INTEREST_PROCESSED, 
  INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY,
  INTEREST_REQUEST_DECLINED,
  OFFER_CREATED,
  OFFER_UPDATED,
  OFFER_STATUS_CHANGED,
  CHAT_MESSAGE_RECEIVE,
  SYSTEM_MESSAGE_RECEIVE,
  TRANSACTION_STATUS_UPDATED,
  NEGOTIATION_STATE_UPDATED,
  NEGOTIATION_FINALIZED,
  NEW_NOTIFICATION,
  type InterestReceivedPayload,
  type InterestProcessedPayload,
  type InterestRequestAcceptedAndChatReadyPayload,
  type YourInterestDeclinedPayload,
  type OfferCreatedPayload,
  type ChatMessageReceivePayload,
  type SystemMessagePayload,
  type TransactionStatusUpdatePayload,
  type PayerNegotiationStatePayload,
  type NegotiationFinalizedPayload,
  type NewNotificationPayload
} from '@/types/socketEvents';

// Event handler type definitions
type EventHandler<T = any> = (payload: T) => void;
type EventHandlers = {
  [INTEREST_RECEIVED]: EventHandler<InterestReceivedPayload>[];
  [INTEREST_PROCESSED]: EventHandler<InterestProcessedPayload>[];
  [INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY]: EventHandler<InterestRequestAcceptedAndChatReadyPayload>[];
  [INTEREST_REQUEST_DECLINED]: EventHandler<YourInterestDeclinedPayload>[];
  [OFFER_CREATED]: EventHandler<OfferCreatedPayload>[];
  [OFFER_UPDATED]: EventHandler<any>[];
  [OFFER_STATUS_CHANGED]: EventHandler<any>[];
  [CHAT_MESSAGE_RECEIVE]: EventHandler<ChatMessageReceivePayload>[];
  [SYSTEM_MESSAGE_RECEIVE]: EventHandler<SystemMessagePayload>[];
  [TRANSACTION_STATUS_UPDATED]: EventHandler<TransactionStatusUpdatePayload>[];
  [NEGOTIATION_STATE_UPDATED]: EventHandler<PayerNegotiationStatePayload>[];
  [NEGOTIATION_FINALIZED]: EventHandler<NegotiationFinalizedPayload>[];
  [NEW_NOTIFICATION]: EventHandler<NewNotificationPayload>[];
  connect: EventHandler<void>[];
  disconnect: EventHandler<Socket.DisconnectReason>[];
  connect_error: EventHandler<Error>[];
};

class CentralizedSocketManager {
  private socket: Socket | null = null;
  private initializationPromise: Promise<Socket> | null = null;
  private isOnlineListenerAdded = false;
  private reconnectionTimeoutId: number | null = null;private handlers: EventHandlers = {
    [INTEREST_RECEIVED]: [],
    [INTEREST_PROCESSED]: [],
    [INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY]: [],
    [INTEREST_REQUEST_DECLINED]: [],
    [OFFER_CREATED]: [],
    [OFFER_UPDATED]: [],
    [OFFER_STATUS_CHANGED]: [],
    [CHAT_MESSAGE_RECEIVE]: [],
    [SYSTEM_MESSAGE_RECEIVE]: [],
    [TRANSACTION_STATUS_UPDATED]: [],
    [NEGOTIATION_STATE_UPDATED]: [],
    [NEGOTIATION_FINALIZED]: [],
    [NEW_NOTIFICATION]: [],
    connect: [],
    disconnect: [],
    connect_error: []
  };
  constructor() {
    console.log('🚀 [CentralizedSocketManager] Creating singleton instance');
    this.setupBrowserOfflineDetection();
  }

  // Browser online/offline event handlers as class methods
  private handleOnline = () => {
    console.log('🌐 [CentralizedSocketManager] Browser back online, checking socket connection...');
    
    // If we have authentication and socket is not connected, try to reconnect
    if (!this.isConnected()) {
      const authStore = useAuthStore();
      if (authStore.isAuthenticated) {
        console.log('[CentralizedSocketManager] Attempting reconnection due to browser online event');
        this.initializeSocket().catch(error => {
          console.error('[CentralizedSocketManager] Failed to reconnect on browser online:', error);
        });
      }
    }
  };

  private handleOffline = () => {
    console.log('🚫 [CentralizedSocketManager] Browser went offline');
    const connectionStore = useConnectionStore();
    connectionStore.setDisconnected('browser_offline');
  };

  // Browser online/offline detection
  private setupBrowserOfflineDetection() {
    if (this.isOnlineListenerAdded) return;
    
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
    
    this.isOnlineListenerAdded = true;
    
    // Initial check
    if (!navigator.onLine) {
      console.log('🚫 [CentralizedSocketManager] Browser is currently offline');
      const connectionStore = useConnectionStore();
      connectionStore.setDisconnected('browser_offline');
    }
  }

  private cleanupBrowserOfflineDetection() {
    if (!this.isOnlineListenerAdded) return;
    
    console.log('[CentralizedSocketManager] Cleaning up browser offline detection listeners');
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
    
    this.isOnlineListenerAdded = false;
  }

  // Register event handlers
  on<T extends keyof EventHandlers>(
    event: T, 
    handler: T extends keyof EventHandlers ? EventHandlers[T][0] : EventHandler
  ): () => void {
    console.log(`🔌 [CentralizedSocketManager] Registering handler for event: ${event}`);
    
    if (!this.handlers[event]) {
      this.handlers[event] = [];
    }
    
    this.handlers[event].push(handler);
    
    // Return unsubscribe function
    return () => {
      const handlers = this.handlers[event];
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
        console.log(`🔌 [CentralizedSocketManager] Unregistered handler for event: ${event}`);
      }
    };
  }

  // Get current socket instance
  getSocket(): Socket | null {
    return this.socket;
  }

  // Check if socket is connected
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Initialize socket connection
  async initializeSocket(): Promise<Socket> {
    console.log('🚀 [CentralizedSocketManager] initializeSocket called');
    
    // If already initializing, return existing promise
    if (this.initializationPromise) {
      console.log('🚀 [CentralizedSocketManager] Already initializing, returning existing promise');
      return this.initializationPromise;
    }

    // If socket exists and is connected, return it
    if (this.socket?.connected) {
      console.log('🚀 [CentralizedSocketManager] Socket already connected, returning existing socket');
      return Promise.resolve(this.socket);
    }

    // Re-setup browser offline detection if it was previously cleaned up
    if (!this.isOnlineListenerAdded) {
      console.log('🚀 [CentralizedSocketManager] Re-setting up browser offline detection');
      this.setupBrowserOfflineDetection();
    }

    // Start initialization
    this.initializationPromise = this._createSocket();
    
    try {
      const socket = await this.initializationPromise;
      this.initializationPromise = null;
      return socket;
    } catch (error) {
      this.initializationPromise = null;
      throw error;
    }
  }

  private async _createSocket(): Promise<Socket> {
    return new Promise((resolve, reject) => {
      try {
        // Clean up existing socket
        if (this.socket && !this.socket.connected) {
          console.log('🚀 [CentralizedSocketManager] Cleaning up disconnected socket');
          this.socket.disconnect();
          this.socket = null;
        }

        const authStore = useAuthStore();
        const token = authStore.token;
        
        if (!token) {
          throw new Error('No authentication token available');
        }

        const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3000';
        console.log('🚀 [CentralizedSocketManager] Creating socket connection to:', backendUrl);
        
        this.socket = io(backendUrl, {
          auth: { token },
          transports: ['websocket', 'polling'],
          timeout: 10000,
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
          reconnectionDelayMax: 5000,        });        // Set up core event listeners
        // Store timeout ID to clear it when connection succeeds or fails
        let connectionTimeoutId: number | null = null;

        this.socket.on('connect', () => {
          console.log('✅ [CentralizedSocketManager] Connected with ID:', this.socket?.id);
          
          // Clear connection timeout since we connected successfully
          if (connectionTimeoutId) {
            clearTimeout(connectionTimeoutId);
            connectionTimeoutId = null;
          }
          
          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setConnected(true);
          
          // Detect transport type and update store
const transportName = this.socket?.io.engine.transport.name;
const validTransports: TransportType[] = ['websocket', 'polling'];
const transport = validTransports.includes(transportName as TransportType) 
  ? transportName as TransportType 
  : 'websocket';
connectionStore.setTransportType(transport);
          
          this._notifyHandlers('connect', undefined);
          resolve(this.socket!);
        });        this.socket.on('disconnect', (reason: Socket.DisconnectReason) => {
          console.log('🔌 [CentralizedSocketManager] Disconnected:', reason);
          
          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setDisconnected(reason);
          
          // Handle different disconnect reasons
          if (reason === 'io server disconnect') {
            // Server initiated disconnect - might be maintenance
            console.log('[CentralizedSocketManager] Server disconnect detected, scheduling reconnect...');
            this.scheduleReconnection();
          } else if (reason === 'transport close' || reason === 'transport error') {
            // Network issues - try to reconnect if browser is online
            if (navigator.onLine) {
              console.log('[CentralizedSocketManager] Network issue detected, scheduling reconnect...');
              this.scheduleReconnection();
            }
          }
          
          this._notifyHandlers('disconnect', reason);
        });        this.socket.on('connect_error', (error: Error) => {
          console.error('❌ [CentralizedSocketManager] Connection error:', error.message);
          
          // Clear connection timeout since we got a definitive error
          if (connectionTimeoutId) {
            clearTimeout(connectionTimeoutId);
            connectionTimeoutId = null;
          }
          
          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setDisconnected(error.message);
          
          // Check if this is a token/auth error vs network error
          if (error.message.toLowerCase().includes('unauthorized') || 
              error.message.toLowerCase().includes('invalid') ||
              error.message.toLowerCase().includes('token')) {
            console.log('[CentralizedSocketManager] Authentication error detected, not scheduling retry');
            this._notifyHandlers('connect_error', error);
            reject(error);
          } else {
            // Network error - schedule retry if user is still authenticated
            const authStore = useAuthStore();
            if (authStore.isAuthenticated && navigator.onLine) {
              console.log('[CentralizedSocketManager] Network error detected, scheduling retry...');
              this.scheduleReconnection();
            }
            this._notifyHandlers('connect_error', error);
            reject(error);
          }
        });

        this.socket.on('reconnect_attempt', () => {
          console.log('🔄 [CentralizedSocketManager] Reconnection attempt...');
          
          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setReconnecting();
        });

        this.socket.on('reconnect', (attemptNumber: number) => {
          console.log(`✅ [CentralizedSocketManager] Reconnected after ${attemptNumber} attempts`);
          
          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setConnected(true);
          connectionStore.resetReconnectAttempts();
        });

        this.socket.on('reconnect_failed', () => {
          console.error('❌ [CentralizedSocketManager] Failed to reconnect after maximum attempts');
          
          // Update connection store
          const connectionStore = useConnectionStore();
          connectionStore.setDisconnected('reconnect_failed');
          
          // Schedule a manual reconnection attempt after a longer delay
          this.scheduleReconnection();
        });

        // Set up application event listeners
        this._setupApplicationEventListeners();        // Connection timeout
        const connectionTimeout = 10000; // 10 seconds
        connectionTimeoutId = setTimeout(() => {
          if (!this.socket?.connected) {
            console.error('⏰ [CentralizedSocketManager] Connection timeout');
            if (this.socket) {
              this.socket.disconnect();
              this.socket = null;
            }
            reject(new Error('Socket connection timeout'));
          }
        }, connectionTimeout);

      } catch (error) {
        console.error('💥 [CentralizedSocketManager] Error creating socket:', error);
        reject(error);
      }
    });
  }

  private _setupApplicationEventListeners(): void {
    if (!this.socket) return;

    console.log('🎯 [CentralizedSocketManager] Setting up application event listeners');

    // Interest events
    this.socket.on(INTEREST_RECEIVED, (payload: InterestReceivedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received INTEREST_RECEIVED:', payload);
      this._notifyHandlers(INTEREST_RECEIVED, payload);
    });

    this.socket.on(INTEREST_PROCESSED, (payload: InterestProcessedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received INTEREST_PROCESSED:', payload);
      this._notifyHandlers(INTEREST_PROCESSED, payload);
    });

    this.socket.on(INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY, (payload: InterestRequestAcceptedAndChatReadyPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY:', payload);
      this._notifyHandlers(INTEREST_REQUEST_ACCEPTED_AND_CHAT_READY, payload);
    });

    this.socket.on(INTEREST_REQUEST_DECLINED, (payload: YourInterestDeclinedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received INTEREST_REQUEST_DECLINED:', payload);
      this._notifyHandlers(INTEREST_REQUEST_DECLINED, payload);
    });

    // Offer events
    this.socket.on(OFFER_CREATED, (payload: OfferCreatedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received OFFER_CREATED:', payload);
      this._notifyHandlers(OFFER_CREATED, payload);
    });

    this.socket.on(OFFER_UPDATED, (payload: any) => {
      console.log('🔔 [CentralizedSocketManager] Received OFFER_UPDATED:', payload);
      this._notifyHandlers(OFFER_UPDATED, payload);
    });

    this.socket.on(OFFER_STATUS_CHANGED, (payload: any) => {
      console.log('🔔 [CentralizedSocketManager] Received OFFER_STATUS_CHANGED:', payload);
      this._notifyHandlers(OFFER_STATUS_CHANGED, payload);
    });

    // Chat events
    this.socket.on(CHAT_MESSAGE_RECEIVE, (payload: ChatMessageReceivePayload) => {
      console.log('🔔 [CentralizedSocketManager] Received CHAT_MESSAGE_RECEIVE:', payload);
      this._notifyHandlers(CHAT_MESSAGE_RECEIVE, payload);
    });    this.socket.on(SYSTEM_MESSAGE_RECEIVE, (payload: SystemMessagePayload) => {
      console.log('🔔 [CentralizedSocketManager] Received SYSTEM_MESSAGE_RECEIVE:', payload);
      this._notifyHandlers(SYSTEM_MESSAGE_RECEIVE, payload);
    });

    // Transaction events
    this.socket.on(TRANSACTION_STATUS_UPDATED, (payload: TransactionStatusUpdatePayload) => {
      console.log('🔔 [CentralizedSocketManager] Received TRANSACTION_STATUS_UPDATED:', payload);
      this._notifyHandlers(TRANSACTION_STATUS_UPDATED, payload);
    });

    // Payer negotiation events
    this.socket.on(NEGOTIATION_STATE_UPDATED, (payload: PayerNegotiationStatePayload) => {
      console.log('🔔 [CentralizedSocketManager] Received NEGOTIATION_STATE_UPDATED:', payload);
      this._notifyHandlers(NEGOTIATION_STATE_UPDATED, payload);
    });    this.socket.on(NEGOTIATION_FINALIZED, (payload: NegotiationFinalizedPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received NEGOTIATION_FINALIZED:', payload);
      this._notifyHandlers(NEGOTIATION_FINALIZED, payload);
    });

    // Notification events
    this.socket.on(NEW_NOTIFICATION, (payload: NewNotificationPayload) => {
      console.log('🔔 [CentralizedSocketManager] Received NEW_NOTIFICATION:', payload);
      this._notifyHandlers(NEW_NOTIFICATION, payload);
    });
  }

  private _notifyHandlers<T extends keyof EventHandlers>(event: T, payload: any): void {
    const handlers = this.handlers[event];
    if (handlers && handlers.length > 0) {
      console.log(`🎯 [CentralizedSocketManager] Notifying ${handlers.length} handlers for event: ${event}`);
      handlers.forEach(handler => {
        try {
          handler(payload);
        } catch (error) {
          console.error(`💥 [CentralizedSocketManager] Error in handler for ${event}:`, error);
        }
      });
    } else {
      console.log(`⚠️ [CentralizedSocketManager] No handlers registered for event: ${event}`);
    }
  }

  // Emit events to server
  emit(event: string, payload: any): void {
    if (this.socket?.connected) {
      console.log(`📤 [CentralizedSocketManager] Emitting ${event}:`, payload);
      this.socket.emit(event, payload);
    } else {
      console.warn(`⚠️ [CentralizedSocketManager] Cannot emit ${event}: socket not connected`);
    }
  }
  // Disconnect socket
  disconnect(): void {
    if (this.socket) {
      console.log('🔌 [CentralizedSocketManager] Disconnecting socket');
      this.socket.disconnect();
      this.socket = null;
    }
    
    // Clear any reconnection timeout
    if (this.reconnectionTimeoutId) {
      clearTimeout(this.reconnectionTimeoutId);
      this.reconnectionTimeoutId = null;
    }
    
    // Clean up browser offline detection on disconnect
    this.cleanupBrowserOfflineDetection();
    
    // Clear handlers
    Object.keys(this.handlers).forEach(event => {
      this.handlers[event as keyof EventHandlers] = [];
    });
  }

  // Schedule reconnection with exponential backoff
  private scheduleReconnection() {
    // Clear any existing reconnection timeout
    if (this.reconnectionTimeoutId) {
      clearTimeout(this.reconnectionTimeoutId);
    }
    
    const authStore = useAuthStore();
    if (!authStore.isAuthenticated) {
      console.log('[CentralizedSocketManager] User not authenticated, skipping reconnection');
      return;
    }
    
    // Schedule reconnection attempt after a delay
    const reconnectDelay = 3000; // 3 seconds
    console.log(`[CentralizedSocketManager] Scheduling reconnection in ${reconnectDelay}ms`);
    
    this.reconnectionTimeoutId = setTimeout(() => {
      if (!this.isConnected() && navigator.onLine && authStore.isAuthenticated) {
        console.log('[CentralizedSocketManager] Attempting scheduled reconnection...');
        this.initializeSocket().catch(error => {
          console.error('[CentralizedSocketManager] Scheduled reconnection failed:', error);
        });
      }
    }, reconnectDelay);
  }
  // Force reconnection manually (can be called from UI)
  forceReconnect(): Promise<Socket> {
    console.log('🔄 [CentralizedSocketManager] Force reconnection requested');
    
    // Disconnect existing socket if any
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    // Clear any existing initialization promise
    this.initializationPromise = null;
    
    // Clear any reconnection timeout
    if (this.reconnectionTimeoutId) {
      clearTimeout(this.reconnectionTimeoutId);
      this.reconnectionTimeoutId = null;
    }
    
    // Ensure browser offline detection is active for reconnection
    if (!this.isOnlineListenerAdded) {
      console.log('🔄 [CentralizedSocketManager] Re-setting up browser offline detection during force reconnect');
      this.setupBrowserOfflineDetection();
    }
    
    // Attempt fresh connection
    return this.initializeSocket();
  }
}

// Create singleton instance
const centralizedSocketManager = new CentralizedSocketManager();

export default centralizedSocketManager;
