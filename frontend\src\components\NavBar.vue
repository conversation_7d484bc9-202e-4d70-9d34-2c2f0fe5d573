<template>
  <n-layout-header bordered class="navbar-header" :class="{ 'rtl-layout': languageStore.isRTL }">    <div class="navbar-container">
      <!-- Mobile Menu Button - Positioned for RTL/LTR conventions -->
      <div class="navbar-actions mobile-hamburger-section">
        <!-- Mobile Menu Button -->
        <n-button
          circle
          @click="toggleMobileMenu"
          :class="{ 'menu-open': showMobileMenu }"
          class="mobile-menu-btn"
        >
          <template #icon>
            <n-icon>
              <MenuOutline v-if="!showMobileMenu" />
              <CloseOutline v-else />
            </n-icon>
          </template>
        </n-button>
      </div>

      <!-- Logo Section -->
      <div class="navbar-logo" @click="$router.push('/')">
        <img :src="themeStore.isDark ? '/logo-dark.webp' : '/logo-light.webp'" alt="MUNygo" class="logo-image" />
        <span class="logo-text">Arz.Ani</span>
      </div>      <!-- Desktop Menu -->
      <div class="navbar-menu desktop-menu">
        <n-menu
          :value="activeKey"
          mode="horizontal"
          :options="menuOptions"          @update:value="handleMenuSelect"
          class="main-menu"
        />
      </div>

      <!-- Mobile Notification Bell - Positioned opposite to hamburger -->
      <div class="navbar-actions mobile-notification-section">
        <!-- Single Notification Bell - Positioned responsively -->
        <div class="notification-bell-container">
          <NotificationBell />
        </div>
      </div><!-- Desktop Actions -->
      <div class="navbar-actions desktop-actions">
        <!-- Notification Bell for Desktop -->
        <div class="notification-bell-container desktop-notification">
          <NotificationBell />
        </div>
          <!-- Connection Status Indicator -->
        <n-tooltip 
          :show-arrow="false" 
          :placement="languageStore.isRTL ? 'bottom-end' : 'bottom-start'"
          :to="false"
          trigger="hover"
          :delay="300"
          class="beautiful-tooltip"
        >
          <template #trigger>
            <n-button 
              circle 
              text 
              class="action-btn connection-status"
              @click="handleConnectionClick"
              :loading="isReconnecting"
            >
              <template #icon>
                <n-icon 
                  size="20" 
                  :color="getConnectionColor()"
                  :class="{ 'pulse-animation': connectionStore.connectionQuality === 'poor' || connectionStore.connectionQuality === 'disconnected' }"
                >
                  <WifiOutline v-if="connectionStore.connectionQuality === 'excellent'" />
                  <WifiOutline v-else-if="connectionStore.connectionQuality === 'good'" />
                  <InformationCircleOutline v-else-if="connectionStore.connectionQuality === 'poor'" />
                  <AlertCircleOutline v-else />
                </n-icon>
              </template>
            </n-button>
          </template>
          {{ getConnectionTooltipText() }}
        </n-tooltip><!-- Language Selector -->
        <LanguageSelector class="action-btn" />
          <!-- Theme Toggle -->
        <n-tooltip 
          :show-arrow="false" 
          :placement="languageStore.isRTL ? 'bottom-end' : 'bottom-start'"
          :to="false"
          trigger="hover"
          :delay="300"
          class="beautiful-tooltip"
        >
          <template #trigger>
            <n-button 
              circle 
              text 
              class="action-btn theme-toggle"
              @click="toggleTheme"
            >
              <template #icon>
                <n-icon size="20" class="theme-icon">
                  <SunnyOutline v-if="themeStore.isDark" />
                  <MoonOutline v-else />
                </n-icon>
              </template>
            </n-button>
          </template>
          {{ getThemeToggleText() }}
        </n-tooltip>        <!-- Debug Report Button (Development) -->
        <div v-if="isDevelopment" class="debug-report-container">
          <DebugReportButtonEnhanced />
        </div>

        <!-- User Avatar with Custom Popover -->
        <div class="user-avatar-container">
          <!-- Custom positioned popover using Teleport -->
          <Teleport to="body">
            <div
              v-if="showUserMenu"
              ref="customUserPopoverRef"
              class="custom-user-popover"
              :style="userPopoverStyle"
              @click.stop
            >
              <!-- Beautiful user menu content with proper theming -->
              <div class="user-dropdown">
                <!-- Profile Option -->
                <div 
                  class="user-menu-item"
                  @click="handleUserMenuSelect('profile')"
                >
                  <n-icon size="18" class="menu-item-icon">
                    <PersonOutline />
                  </n-icon>
                  <span class="menu-item-text">{{ t('navbar.profile') }}</span>
                </div>
                
                <!-- Messages Option -->
                <div 
                  class="user-menu-item"
                  @click="handleUserMenuSelect('messages')"
                >
                  <n-icon size="18" class="menu-item-icon">
                    <ChatbubbleOutline />
                  </n-icon>
                  <span class="menu-item-text">{{ t('navbar.messages') }}</span>
                </div>
                
                <!-- Divider -->
                <div class="user-menu-divider"></div>
                
                <!-- Logout Option -->
                <div 
                  class="user-menu-item user-menu-item--logout"
                  @click="handleUserMenuSelect('logout')"
                >
                  <n-icon size="18" class="menu-item-icon">
                    <LogOutOutline />
                  </n-icon>
                  <span class="menu-item-text">{{ t('navbar.logout') }}</span>
                </div>
              </div>
            </div>
          </Teleport>
          
          <!-- User avatar trigger -->
          <n-button 
            ref="avatarButtonRef"
            circle 
            class="user-avatar" 
            @click="toggleUserMenu"
          >
            <template #icon>
              <n-icon><PersonOutline /></n-icon>
            </template>
          </n-button>
        </div>
      </div>
    </div>    <!-- Mobile Menu Drawer -->
    <n-drawer
      v-model:show="showMobileMenu"
      :width="300"
      :placement="languageStore.isRTL ? 'right' : 'left'"
      class="mobile-drawer"
    >
      <n-drawer-content 
        :title="getMenuTitle()" 
        closable
        :class="{ 'rtl-content': languageStore.isRTL }"
      >        <div class="mobile-menu-content">
          <!-- Mobile Navigation Menu -->
          <n-menu
            :value="activeKey"
            :options="menuOptions"
            @update:value="handleMobileMenuSelect"
            class="mobile-menu-items"
          />

          <!-- Mobile Actions -->
          <div class="mobile-actions">
            <!-- Connection Status -->
            <div class="mobile-action-item">
              <n-icon size="20" :color="getConnectionColor()">
                <WifiOutline v-if="connectionStore.connectionQuality === 'excellent'" />
                <WifiOutline v-else-if="connectionStore.connectionQuality === 'good'" />
                <InformationCircleOutline v-else-if="connectionStore.connectionQuality === 'poor'" />
                <AlertCircleOutline v-else />
              </n-icon>
              <span>{{ getConnectionStatusText() }}</span>
            </div>            <!-- Language Selector -->
            <div class="mobile-action-item">
              <LanguageSelector />
            </div>

            <!-- Theme Toggle -->
            <div class="mobile-action-item" @click="toggleTheme">
              <n-icon size="20" class="theme-icon">
                <SunnyOutline v-if="themeStore.isDark" />
                <MoonOutline v-else />
              </n-icon>
              <span>{{ getThemeToggleText() }}</span>
            </div>            <!-- Debug Report Button (Development) -->
            <div v-if="isDevelopment" class="mobile-action-item debug-report-mobile">
              <DebugReportButtonEnhanced />
            </div>

            <!-- Messages -->
            <div class="mobile-action-item" @click="handleChatClick">
              <n-icon size="20"><ChatbubbleOutline /></n-icon>
              <span>{{ getMessagesLabel() }}</span>
            </div>
            
            <!-- Profile -->
            <div class="mobile-action-item" @click="navigateToProfile">
              <n-icon size="20"><PersonOutline /></n-icon>
              <span>{{ getProfileLabel() }}</span>
            </div>
            
            <!-- Logout -->
            <div class="mobile-action-item logout" @click="handleLogout">
              <n-icon size="20"><LogOutOutline /></n-icon>
              <span>{{ getLogoutLabel() }}</span>
            </div>
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>
  </n-layout-header>
</template>

<script setup lang="ts">
import { useDropdownPosition } from '@/composables/useDropdownPosition'
import { ref, computed, watch, h, nextTick, Teleport, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  NLayoutHeader,
  NMenu,
  NButton,
  NIcon,
  NDrawer,
  NDrawerContent,
  NTooltip,
  useMessage
} from 'naive-ui'
import {
  PersonOutline,
  MenuOutline,
  CloseOutline,
  ChatbubbleOutline,
  LogOutOutline,
  WifiOutline,
  InformationCircleOutline,
  AlertCircleOutline,
  SunnyOutline,
  MoonOutline
} from '@vicons/ionicons5'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'
import { useConnectionStore } from '@/stores/connection'
import { useLanguageStore } from '@/stores/language'
import centralizedSocketManager from '@/services/centralizedSocketManager'
import NotificationBell from './NotificationBell.vue'
import LanguageSelector from './LanguageSelector.vue'
import DebugReportButtonEnhanced from './DebugReportButtonEnhanced.vue'
import { checkAdminAccess } from '@/utils/adminUtils'

// Stores and utilities
const authStore = useAuthStore()
const themeStore = useThemeStore()
const connectionStore = useConnectionStore()
const languageStore = useLanguageStore()
const route = useRoute()
const router = useRouter()
const message = useMessage()

// Reactive state
const showMobileMenu = ref(false)
const isReconnecting = ref(false)

// Development environment check - show in dev OR if explicitly enabled
const isDevelopment = computed(() => 
  import.meta.env.DEV || localStorage.getItem('showDebugReport') === 'true'
)

// Admin access check
const isUserAdmin = computed(() => checkAdminAccess(authStore.user?.email))

// Custom user menu state
const showUserMenu = ref(false)
const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1024)

// Template refs
const avatarButtonRef = ref<HTMLElement>()
const customUserPopoverRef = ref<HTMLElement>()

// User popover positioning
const userPopoverPosition = ref({
  left: '20px',
  top: '60px',
  width: '200px'
})

const { positionAllDropdowns } = useDropdownPosition();


// Debug logging functions
const logElementPosition = (element: Element, name: string) => {
  const rect = element.getBoundingClientRect();
  const computed = window.getComputedStyle(element);
  console.log(`🎯 [${name}] Position Debug:`, {
    element: element,
    boundingRect: {
      left: rect.left,
      right: rect.right,
      top: rect.top,
      bottom: rect.bottom,
      width: rect.width,
      height: rect.height,
      centerX: rect.left + rect.width / 2,
      centerY: rect.top + rect.height / 2
    },
    computedStyles: {
      position: computed.position,
      left: computed.left,
      right: computed.right,
      top: computed.top,
      transform: computed.transform,
      transformOrigin: computed.transformOrigin,
      direction: computed.direction
    },
    viewport: {
      windowWidth: window.innerWidth,
      windowHeight: window.innerHeight,
      scrollX: window.scrollX,
      scrollY: window.scrollY
    }
  });
};

// Force correct dropdown positioning
const forceDropdownPositioning = () => {
  // Use requestAnimationFrame for smoother rendering instead of setTimeout
  requestAnimationFrame(() => {
    positionAllDropdowns();
  });
};

const logDropdownPositioning = () => {
  setTimeout(() => {
    console.log('🔍 === DROPDOWN POSITIONING ANALYSIS ===');
    
    // Log trigger buttons
    const languageBtn = document.querySelector('.language-selector');
    const themeBtn = document.querySelector('.theme-toggle');
    const avatarBtn = document.querySelector('.user-avatar');
    const notificationBtn = document.querySelector('.notification-button');
    const connectionBtn = document.querySelector('.connection-status');

    if (languageBtn) logElementPosition(languageBtn, 'Language Button');
    if (themeBtn) logElementPosition(themeBtn, 'Theme Button');
    if (avatarBtn) logElementPosition(avatarBtn, 'Avatar Button');
    if (notificationBtn) logElementPosition(notificationBtn, 'Notification Button');
    if (connectionBtn) logElementPosition(connectionBtn, 'Connection Button');

    // Log dropdown menus
    const dropdownMenus = document.querySelectorAll('.n-dropdown-menu');
    dropdownMenus.forEach((menu, index) => {
      logElementPosition(menu, `Dropdown Menu ${index + 1}`);
    });

    // Log popover content
    const popoverContent = document.querySelectorAll('.n-popover-content');
    popoverContent.forEach((content, index) => {
      logElementPosition(content, `Popover Content ${index + 1}`);
    });

    // Log navbar container
    const navbarContainer = document.querySelector('.navbar-container');
    if (navbarContainer) logElementPosition(navbarContainer, 'Navbar Container');

    // Log navbar actions
    const navbarActions = document.querySelector('.navbar-actions');
    if (navbarActions) logElementPosition(navbarActions, 'Navbar Actions');

    console.log(`🌐 Current Language: ${languageStore.currentLanguage}`);
    console.log(`🔄 Is RTL: ${languageStore.isRTL}`);
    console.log(`📱 Document Direction: ${document.documentElement.getAttribute('dir')}`);
    console.log('🔍 === END ANALYSIS ===');
    
    // Apply forced positioning
    forceDropdownPositioning();
  }, 100);
};

// Computed properties
const activeKey = computed(() => {
  const path = route.path
  if (path === '/') return 'home'
  if (path.startsWith('/browse-offers')) return 'browse'
  if (path.startsWith('/create-offer')) return 'create'
  if (path.startsWith('/my-offers')) return 'my-offers'
  if (path.startsWith('/admin/debug-dashboard')) return 'admin-debug'
  return null
})

// Menu configuration with translations
const menuOptions = computed(() => {
  const baseOptions = [
    {
      label: languageStore.currentLanguage === 'fa' ? 'خانه' : 'Home',
      key: 'home',
      icon: () => h(NIcon, null, { default: () => h('i', { class: 'fas fa-home' }) })
    },
    {
      label: languageStore.currentLanguage === 'fa' ? 'مرور پیشنهادات' : 'Browse Offers',
      key: 'browse',
      icon: () => h(NIcon, null, { default: () => h('i', { class: 'fas fa-search' }) })
    },
    {
      label: languageStore.currentLanguage === 'fa' ? 'ایجاد پیشنهاد' : 'Create Offer',
      key: 'create',
      icon: () => h(NIcon, null, { default: () => h('i', { class: 'fas fa-plus' }) })
    },
    {
      label: languageStore.currentLanguage === 'fa' ? 'پیشنهادات من' : 'My Offers',
      key: 'my-offers',
      icon: () => h(NIcon, null, { default: () => h('i', { class: 'fas fa-list' }) })
    }
  ]

  // Add admin menu option if user is admin
  if (isUserAdmin.value) {
    baseOptions.push({
      label: languageStore.currentLanguage === 'fa' ? 'مدیریت سیستم' : 'Admin Dashboard',
      key: 'admin-debug',
      icon: () => h(NIcon, null, { default: () => h('i', { class: 'fas fa-cogs' }) })
    })
  }

  return baseOptions
})

// Helper functions for translations
const getMenuTitle = () => languageStore.currentLanguage === 'fa' ? 'منو' : 'Menu'
const getMessagesLabel = () => languageStore.currentLanguage === 'fa' ? 'پیام‌ها' : 'Messages'
const getProfileLabel = () => languageStore.currentLanguage === 'fa' ? 'پروفایل' : 'Profile'
const getLogoutLabel = () => languageStore.currentLanguage === 'fa' ? 'خروج' : 'Logout'

// Translation function for the new popover
const t = (key: string) => {
  switch (key) {
    case 'navbar.profile':
      return languageStore.currentLanguage === 'fa' ? 'پروفایل' : 'Profile'
    case 'navbar.messages':
      return languageStore.currentLanguage === 'fa' ? 'پیام‌ها' : 'Messages'
    case 'navbar.logout':
      return languageStore.currentLanguage === 'fa' ? 'خروج' : 'Logout'
    default:
      return key
  }
}

const getThemeToggleText = () => {
  if (themeStore.isDark) {
    return languageStore.currentLanguage === 'fa' ? 'حالت روشن' : 'Light Mode'
  } else {
    return languageStore.currentLanguage === 'fa' ? 'حالت تاریک' : 'Dark Mode'
  }
}

const getConnectionStatusText = () => {
  const quality = connectionStore.connectionQuality
  if (languageStore.currentLanguage === 'fa') {
    switch (quality) {
      case 'excellent': return 'متصل - به‌روزرسانی لحظه‌ای'
      case 'good': return 'متصل - حالت محدود'
      case 'poor': return 'در حال اتصال مجدد...'
      case 'disconnected': return 'قطع شده - تلاش برای اتصال مجدد'
      default: return 'وضعیت اتصال نامشخص'
    }
  } else {
    return connectionStore.connectionStatus
  }
}

// Methods
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const handleMenuSelect = (key: string) => {
  navigateToRoute(key)
}

const handleMobileMenuSelect = (key: string) => {
  closeMobileMenu()
  navigateToRoute(key)
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

const navigateToRoute = (key: string) => {
  switch (key) {
    case 'home':
      router.push('/')
      break
    case 'browse':
      router.push('/browse-offers')
      break
    case 'create':
      router.push('/create-offer')
      break
    case 'my-offers':
      router.push('/my-offers')
      break
    case 'admin-debug':
      router.push('/admin/debug-dashboard')
      break
  }
}

// Custom user popover positioning and control
const calculateUserPopoverPosition = () => {
  if (!avatarButtonRef.value) {
    console.log('[NavBar] avatarButtonRef not ready for positioning');
    return;
  }

  try {
    // Get the actual DOM element from the Vue component instance
    const avatarElement = (avatarButtonRef.value as any)?.$el || avatarButtonRef.value;
    if (!avatarElement || typeof avatarElement.getBoundingClientRect !== 'function') {
      console.log('[NavBar] Avatar DOM element not ready for positioning');
      return;
    }

    const rect = avatarElement.getBoundingClientRect();
    const isMobile = windowWidth.value <= 768;
    const isRtl = languageStore.isRTL;
    
    // Base margin and width calculations
    const baseMargin = isMobile ? 16 : 20;
    const popoverWidth = isMobile ? 180 : 200;
    
    let left: number;
    let top = rect.bottom + 12; // 12px gap below the avatar
    
    if (isRtl) {
      // RTL: Align popover to the right edge of the avatar
      if (isMobile) {
        const preferredLeft = rect.right - (popoverWidth * 0.8); // 80% of popover width from right edge
        left = preferredLeft;
        
        // Ensure popover doesn't go off left edge
        if (left < baseMargin) {
          left = baseMargin;
        }
        // Ensure popover doesn't go off right edge
        if (left + popoverWidth > window.innerWidth - baseMargin) {
          left = window.innerWidth - popoverWidth - baseMargin;
        }
      } else {
        // Desktop RTL: Align to right edge of avatar
        left = rect.right - popoverWidth;
        if (left < baseMargin) {
          left = baseMargin;
        }
      }
    } else {
      // LTR: Align popover to the left edge of the avatar
      if (isMobile) {
        const preferredLeft = rect.left - (popoverWidth * 0.2); // 20% of popover width before left edge
        left = preferredLeft;
        
        // Ensure popover doesn't go off right edge
        if (left + popoverWidth > window.innerWidth - baseMargin) {
          left = window.innerWidth - popoverWidth - baseMargin;
        }
        // Ensure popover doesn't go off left edge
        if (left < baseMargin) {
          left = baseMargin;
        }
      } else {
        // Desktop LTR: Align to left edge of avatar
        left = rect.left;
        if (left + popoverWidth > window.innerWidth - baseMargin) {
          left = window.innerWidth - popoverWidth - baseMargin;
        }
      }
    }

    userPopoverPosition.value = {
      left: `${left}px`,
      top: `${top}px`,
      width: `${popoverWidth}px`
    };

    console.log('[NavBar] User popover position calculated:', userPopoverPosition.value);
  } catch (error) {
    console.error('[NavBar] Error calculating user popover position:', error);
  }
};

// User popover style
const userPopoverStyle = computed(() => {
  const isDark = themeStore.isDark;
  
  return {
    position: 'fixed' as const,
    left: userPopoverPosition.value.left,
    top: userPopoverPosition.value.top,
    width: userPopoverPosition.value.width,
    zIndex: 9999,
    backgroundColor: isDark ? '#2d3748' : '#ffffff',
    border: `1px solid ${isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.15)'}`,
    borderRadius: '8px',
    boxShadow: isDark 
      ? '0 8px 24px rgba(0, 0, 0, 0.4), 0 4px 8px rgba(0, 0, 0, 0.2)'
      : '0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)',
    overflow: 'hidden' as const,
    boxSizing: 'border-box' as const,
    contain: 'layout' as const
  }
});

// Toggle user menu visibility
const toggleUserMenu = async (event: Event) => {
  event.stopPropagation();
  console.log('[NavBar] User menu toggle clicked, current showUserMenu:', showUserMenu.value);
  
  if (!showUserMenu.value) {
    showUserMenu.value = true;
    await nextTick();
    setTimeout(() => {
      calculateUserPopoverPosition();
    }, 10);
  } else {
    showUserMenu.value = false;
  }
};

// Close user menu
const closeUserMenu = () => {
  console.log('[NavBar] closeUserMenu called');
  showUserMenu.value = false;
};

// Handle clicks outside to close user menu
const handleUserMenuClickOutside = (event: Event) => {
  if (!showUserMenu.value) {
    return;
  }

  const target = event.target as HTMLElement;
  
  // Check if click is inside the popover
  if (customUserPopoverRef.value && customUserPopoverRef.value.contains(target)) {
    return;
  }
  
  // Check if click is on the avatar button
  const avatarElement = (avatarButtonRef.value as any)?.$el || avatarButtonRef.value;
  if (avatarElement && typeof avatarElement.contains === 'function' && avatarElement.contains(target)) {
    return;
  }
  
  // Click is outside - close the menu
  console.log('[NavBar] Click outside user menu detected, closing');
  closeUserMenu();
};

const handleUserMenuSelect = (key: string) => {
  console.log('👤 User menu selected:', key);
  
  // Close the menu first
  closeUserMenu();
  
  switch (key) {
    case 'profile':
      router.push('/profile')
      break
    case 'messages':
      // Navigate to messages/chat
      router.push('/') // For now, just go to home
      break
    case 'logout':
      handleLogout()
      break
  }
}

const navigateToProfile = () => {
  closeMobileMenu()
  router.push('/profile')
}

const handleChatClick = () => {
  closeMobileMenu()
  // Handle chat click logic
}

const handleLogout = async () => {
  closeMobileMenu()
  
  // Clear auth state
  authStore.logout()
  
  // Wait for the next tick to ensure auth store state is updated
  await nextTick()
  
  // Navigate to login page using replace to avoid history issues
  await router.replace('/login')
  
  // Show success message after successful navigation
  const successMsg = languageStore.currentLanguage === 'fa' ? 'با موفقیت خارج شدید' : 'Logged out successfully'
  message.success(successMsg)
}

const toggleTheme = () => {
  console.log('🌙 Theme toggle clicked, logging positions...');
  logDropdownPositioning();
  themeStore.toggleTheme()
  const themeMsg = themeStore.isDark ?
    (languageStore.currentLanguage === 'fa' ? 'تم تاریک فعال شد' : 'Dark theme enabled') :
    (languageStore.currentLanguage === 'fa' ? 'تم روشن فعال شد' : 'Light theme enabled')
  message.info(themeMsg)
}

// Connection status helper
const getConnectionColor = () => {
  switch (connectionStore.connectionQuality) {
    case 'excellent':
      return '#18a058' // Green for excellent connection
    case 'good':
      return '#f0a020' // Orange for good connection
    case 'poor':
      return '#d03050' // Red for poor connection
    case 'disconnected':
      return '#d03050' // Red for disconnected
    default:
      return '#666666' // Gray for unknown
  }
}

// Connection handling methods
const handleConnectionClick = async () => {
  if (connectionStore.connectionQuality === 'disconnected' || connectionStore.connectionQuality === 'poor') {
    console.log('🔄 [NavBar] Manual reconnection requested');
    isReconnecting.value = true;
    
    try {
      await centralizedSocketManager.forceReconnect();
      const successMsg = languageStore.currentLanguage === 'fa' 
        ? 'اتصال مجددا برقرار شد' 
        : 'Connection restored';
      message.success(successMsg);
    } catch (error) {
      console.error('[NavBar] Reconnection failed:', error);
      const errorMsg = languageStore.currentLanguage === 'fa' 
        ? 'خطا در برقراری اتصال مجدد' 
        : 'Failed to reconnect';
      message.error(errorMsg);
    } finally {
      isReconnecting.value = false;
    }
  }
};

const getConnectionTooltipText = () => {
  const quality = connectionStore.connectionQuality;
  const baseText = getConnectionStatusText();
  
  if (quality === 'disconnected' || quality === 'poor') {
    const clickText = languageStore.currentLanguage === 'fa' 
      ? ' - کلیک برای اتصال مجدد' 
      : ' - Click to reconnect';
    return baseText + clickText;
  }
  
  return baseText;
};

// Watch for route changes to close mobile menu
watch(route, () => {
  if (showMobileMenu.value) {
    closeMobileMenu()
  }
  // Also close user menu on route changes
  if (showUserMenu.value) {
    closeUserMenu()
  }
})

// Lifecycle hooks for user menu
onMounted(() => {
  if (typeof window !== 'undefined') {
    const updateWindowWidth = () => {
      windowWidth.value = window.innerWidth;
      // Recalculate position if user menu is visible
      if (showUserMenu.value) {
        calculateUserPopoverPosition();
      }
    };
    
    window.addEventListener('resize', updateWindowWidth);
    // Use capture phase to handle clicks before other handlers
    document.addEventListener('click', handleUserMenuClickOutside, true);
    
    onUnmounted(() => {
      window.removeEventListener('resize', updateWindowWidth);
      document.removeEventListener('click', handleUserMenuClickOutside, true);
    });
  }
});
</script>

<style scoped>

/* ===== BASE NAVBAR STYLES ===== */
.navbar-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
}

/* Light theme navbar */
[data-theme="light"] .navbar-header {
  background: rgba(255, 255, 255, 0.95);
  border-bottom-color: #e0e0e6;
  color: #333333;
}

/* Dark theme navbar */
[data-theme="dark"] .navbar-header {
  background: rgba(26, 26, 46, 0.95);
  border-bottom-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 16px;
  max-width: 1400px; /* Increased max-width for better desktop spacing */
  margin: 0 auto;
  position: relative;
  overflow: visible; /* Ensure popovers are visible */
}

/* Mobile layout stability */
@media (max-width: 968px) {
  .navbar-container {
    overflow: visible; /* Prevent clipping of absolutely positioned elements */
  }
}

/* RTL Layout Support */
.rtl-layout .navbar-container {
  direction: rtl;
}

/* ===== LOGO SECTION ===== */
.navbar-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0; /* Prevent logo from shrinking */
  min-width: 120px;
}

/* Mobile logo centering */
@media (max-width: 968px) {
  .navbar-logo {
    margin: 0 auto; /* Center the logo */
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
}

.navbar-logo:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

.logo-image {
  height: 48px;
  width: auto;
  transition: transform 0.3s ease;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  transition: color 0.3s ease;
  display: none;
}

/* Light theme logo */
[data-theme="light"] .logo-text {
  color: #18a058;
}

/* Dark theme logo */
[data-theme="dark"] .logo-text {
  color: #63e2b7;
}

/* ===== DESKTOP MENU ===== */
.navbar-menu {
  display: flex;
  align-items: center;
  margin-left: 20px; /* Space from logo */
}

.desktop-menu {
  display: flex;
  align-items: center;
}

.main-menu {
  background: transparent !important;
  width: auto !important; /* Let menu take natural width */
}

/* Let menu items take natural space */
.main-menu .n-menu-item {
  padding: 0 8px !important; /* Natural spacing between items */
  min-width: fit-content !important;
}

.main-menu .n-menu-item-content {
  padding: 8px 16px !important; /* Comfortable padding for text */
  white-space: nowrap !important;
  min-width: fit-content !important;
  overflow: visible !important; /* Ensure text is never cut off */
}

/* Additional Naive UI menu optimizations for text readability */
.n-menu.n-menu--horizontal {
  min-width: fit-content !important;
  overflow: visible !important;
}

.n-menu.n-menu--horizontal .n-menu-item {
  min-width: fit-content !important;
  flex-shrink: 0 !important;
}

.n-menu.n-menu--horizontal .n-menu-item .n-menu-item-content {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: initial !important;
  min-width: fit-content !important;
}

/* ===== SHARED NOTIFICATION BELL ===== */
.notification-bell-container {
  display: flex;
  align-items: center;
  z-index: 10;
  flex-shrink: 0; /* Prevent notification bell from shrinking */
}

/* Desktop notification bell - flows naturally in desktop actions */
.desktop-actions .notification-bell-container {
  order: 1; /* Show first in desktop actions */
  margin-right: 8px; /* Add spacing from other desktop items */
}

.rtl-layout .desktop-actions .notification-bell-container {
  margin-right: 0;
  margin-left: 8px;
}

/* ===== DESKTOP ACTIONS ===== */
.navbar-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: fit-content; /* Changed from 200px to fit-content for better responsiveness */
  justify-content: flex-end;
  position: relative;
  flex-shrink: 0; /* Prevent actions from shrinking */
}

.desktop-actions {
  max-width: 300px; /* Limit max width to prevent spreading */
  overflow: visible; /* Ensure dropdowns are visible */
}

.rtl-layout .navbar-actions {
  justify-content: flex-start;
}

.action-btn {
  transition: all 0.3s ease;
  position: relative;
  flex-shrink: 0; /* Prevent individual buttons from shrinking */
  width: 40px; /* Fixed width for consistency */
  height: 40px; /* Fixed height for consistency */
}

.action-btn:hover {
  transform: translateY(-1px);
}

/* Specific positioning for dropdown triggers */
.language-selector,
.user-avatar {
  position: relative;
}

/* Ensure dropdowns don't overflow viewport */
:deep(.n-dropdown) {
  max-width: 250px;
}

:deep(.n-tooltip) {
  max-width: 200px;
}

/* Connection Status Animation */
.connection-status .pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1);
  }
}

/* Theme Toggle Animation */
.theme-toggle .theme-icon {
  transition: transform 0.5s ease;
}

.theme-toggle:hover .theme-icon {
  transform: rotate(180deg);
}

/* User Avatar Styling */
.user-avatar {
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

[data-theme="light"] .user-avatar {
  background: linear-gradient(135deg, #18a058, #36ad6a);
  color: white;
}

[data-theme="dark"] .user-avatar {
  background: linear-gradient(135deg, #63e2b7, #36ad6a);
  color: #1a1a2e;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(24, 160, 88, 0.3);
}

/* ===== MOBILE HAMBURGER SECTION ===== */
.mobile-hamburger-section {
  display: none; /* Hidden by default, shown on mobile */
}

/* Mobile positioning only */
@media (max-width: 968px) {
  .mobile-hamburger-section {
    position: absolute;
    left: 12px; /* Position on left for LTR */
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 40px; /* Fixed width to prevent layout shifts */
    height: 40px; /* Fixed height to prevent layout shifts */
  }
  
  .rtl-layout .mobile-hamburger-section {
    left: auto;
    right: 12px; /* Position on right for RTL, away from logo */
  }
}

/* ===== MOBILE NOTIFICATION SECTION ===== */
.mobile-notification-section {
  display: none; /* Hidden by default, shown on mobile */
}

/* Mobile positioning only */
@media (max-width: 968px) {
  .mobile-notification-section {
    position: absolute;
    right: 12px; /* Position on right for LTR */
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 40px; /* Fixed width to prevent layout shifts */
    height: 40px; /* Fixed height to prevent layout shifts */
  }
  
  .rtl-layout .mobile-notification-section {
    right: auto;
    left: 12px; /* Position on left for RTL, away from logo */
  }
}

/* ===== MOBILE MENU BUTTON ===== */
.mobile-menu-button {
  display: none;
}

.mobile-menu-btn {
  transition: all 0.3s ease;
}

.mobile-menu-btn.menu-open {
  transform: rotate(90deg);
}

/* Light theme mobile button */
[data-theme="light"] .mobile-menu-btn.menu-open {
  background-color: #18a058 !important;
  color: white !important;
}

/* Dark theme mobile button */
[data-theme="dark"] .mobile-menu-btn.menu-open {
  background-color: #63e2b7 !important;
  color: #1a1a2e !important;
}

/* ===== MOBILE DRAWER ===== */
.mobile-drawer {
  z-index: 2000;
}

.mobile-menu-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px 0;
}

.mobile-menu-items {
  flex: 1;
  background: transparent !important;
  margin-bottom: 16px;
}

.mobile-actions {
  border-top: 1px solid;
  padding-top: 16px;
  margin-top: auto;
}

/* Light theme mobile actions */
[data-theme="light"] .mobile-actions {
  border-top-color: #e0e0e6;
}

/* Dark theme mobile actions */
[data-theme="dark"] .mobile-actions {
  border-top-color: rgba(255, 255, 255, 0.1);
}

/* ===== MOBILE ACTION ITEMS ===== */
.mobile-action-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  margin: 2px 0;
}

.rtl-layout .mobile-action-item {
  flex-direction: row-reverse;
  text-align: right;
}

/* Light theme mobile action item */
[data-theme="light"] .mobile-action-item:hover {
  background-color: #f5f5f5;
  transform: translateX(4px);
}

[data-theme="light"] .rtl-layout .mobile-action-item:hover {
  transform: translateX(-4px);
}

/* Dark theme mobile action item */
[data-theme="dark"] .mobile-action-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

[data-theme="dark"] .rtl-layout .mobile-action-item:hover {
  transform: translateX(-4px);
}

/* Language Item Special Styling */
.language-item {
  justify-content: space-between;
}

.rtl-layout .language-item {
  flex-direction: row;
}

.current-lang {
  font-size: 12px;
  opacity: 0.7;
  margin-left: auto;
}

.rtl-layout .current-lang {
  margin-left: 0;
  margin-right: auto;
}

.expand-icon {
  transition: transform 0.3s ease;
  margin-left: 8px;
}

.rtl-layout .expand-icon {
  margin-left: 0;
  margin-right: 8px;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* ===== LANGUAGE OPTIONS ===== */
.language-options {
  margin: 8px 0 8px 32px;
  border-left: 2px solid;
  padding-left: 16px;
}

.rtl-layout .language-options {
  margin: 8px 32px 8px 0;
  border-left: none;
  border-right: 2px solid;
  padding-left: 0;
  padding-right: 16px;
}

/* Light theme language options */
[data-theme="light"] .language-options {
  border-left-color: #e0e0e6;
}

[data-theme="light"] .rtl-layout .language-options {
  border-right-color: #e0e0e6;
}

/* Dark theme language options */
[data-theme="dark"] .language-options {
  border-left-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .rtl-layout .language-options {
  border-right-color: rgba(255, 255, 255, 0.2);
}

.language-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
  justify-content: space-between;
}

.rtl-layout .language-option {
  flex-direction: row-reverse;
}

.language-option:hover {
  background-color: rgba(24, 160, 88, 0.1);
}

.language-option.active {
  background-color: rgba(24, 160, 88, 0.2);
  font-weight: 500;
}

.flag-icon {
  font-size: 16px;
  min-width: 20px;
}

/* ===== DEBUG REPORT BUTTON ===== */
.debug-report-container {
  display: inline-flex;
  align-items: center;
}

.mobile-action-item.debug-report-mobile {
  padding: 8px 16px;
  border: 1px dashed;
  background: rgba(56, 178, 172, 0.1);
}

[data-theme="light"] .mobile-action-item.debug-report-mobile {
  border-color: rgba(56, 178, 172, 0.3);
  color: #0d9488;
}

[data-theme="light"] .mobile-action-item.debug-report-mobile:hover {
  background: rgba(56, 178, 172, 0.15);
  border-color: rgba(56, 178, 172, 0.5);
}

[data-theme="dark"] .mobile-action-item.debug-report-mobile {
  border-color: rgba(56, 178, 172, 0.4);
  color: #5eead4;
}

[data-theme="dark"] .mobile-action-item.debug-report-mobile:hover {
  background: rgba(56, 178, 172, 0.2);
  border-color: rgba(56, 178, 172, 0.6);
}

/* ===== LOGOUT SPECIAL STYLING ===== */
.mobile-action-item.logout {
  color: #d03050;
  margin-top: 16px;
  border-top: 1px solid;
  padding-top: 16px;
}

/* Light theme logout */
[data-theme="light"] .mobile-action-item.logout {
  border-top-color: #fecaca;
}

[data-theme="light"] .mobile-action-item.logout:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

/* Dark theme logout */
[data-theme="dark"] .mobile-action-item.logout {
  border-top-color: rgba(208, 48, 80, 0.3);
}

[data-theme="dark"] .mobile-action-item.logout:hover {
  background-color: rgba(208, 48, 80, 0.1);
  color: #f87171;
}

/* ===== NOTIFICATION BADGE ===== */
.notification-badge {
  margin-left: auto;
}

.rtl-layout .notification-badge {
  margin-left: 0;
  margin-right: auto;
}

/* ===== RESPONSIVE DESIGN ===== */
.dropdown-centered {
  position: fixed !important;
  right: auto !important;
  transform: translateX(-50%) !important;
  transform-origin: top center !important;
  z-index: 1001 !important;
}

/* Desktop: Show desktop notification, hide mobile sections */
@media (min-width: 969px) {
  .mobile-hamburger-section,
  .mobile-notification-section {
    display: none !important;
  }
    .desktop-actions .notification-bell-container.desktop-notification {
    display: flex !important;
  }
}

/* Large desktop: Optimal spacing */
@media (min-width: 1201px) {
  .navbar-container {
    padding: 0 20px;
  }
  
  .desktop-actions {
    gap: 10px;
  }
}

/* Medium desktop: Balanced layout */
@media (max-width: 1200px) and (min-width: 969px) {
  .navbar-container {
    padding: 0 16px;
  }
  
  .desktop-actions {
    gap: 8px;
  }
  
  .action-btn {
    width: 36px;
    height: 36px;
  }
}

/* Mobile: Show mobile sections, hide desktop actions */
@media (max-width: 968px) {
  .desktop-actions .notification-bell-container {
    display: none !important;
  }
  
  .mobile-hamburger-section,
  .mobile-notification-section {
    display: flex !important;
  }
  
  .navbar-container {
    padding: 0 12px;
  }
  
  .desktop-menu,
  .desktop-actions {
    display: none;
  }
  
  .logo-text {
    display: inline;
  }
  
  /* Ensure logo stays in center column */
  .navbar-logo {
    grid-column: 2;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    padding: 0 8px;
  }
    .logo-image {
    height: 42px;
  }
  
  .logo-text {
    font-size: 18px;
  }
  
  .mobile-menu-content {
    padding: 8px 0;
  }
    .mobile-hamburger-section,
  .mobile-notification-section {
    gap: 6px;
  }
}

/* Large Desktop screens (1201px+): Maximum menu readability optimization */
@media (min-width: 1201px) {
  .navbar-container {
    max-width: 1500px; /* Even more width on very large screens */
    padding: 0 32px; /* More generous padding */
  }
  
  .navbar-menu {
    min-width: 550px; /* Even more space for menu on large screens */
  }
  
  .desktop-menu {
    margin: 0 24px; /* More generous margins on very large screens */
    max-width: 1000px; /* Increased max width for excellent text display */
  }
  
  .desktop-actions {
    gap: 12px; /* Larger gap on very large screens */
    min-width: 220px; /* More space for actions */
  }
  
  /* Enhanced menu item spacing for large screens */
  .main-menu .n-menu-item {
    padding: 0 24px !important; /* Very generous padding on large screens */
  }
  
  .main-menu .n-menu-item-content {
    padding: 12px 18px !important; /* Increased padding for excellent readability */
    font-size: 15px; /* Slightly larger font for better readability */
  }
}

/* ===== NAIVE UI OVERRIDES ===== */
:deep(.n-menu.n-menu--horizontal .n-menu-item) {
  padding: 0 16px;
  border-radius: 8px;
  margin: 0 4px;
  transition: all 0.3s ease;
}

:deep(.n-menu.n-menu--horizontal) {
  background: transparent !important;
}

/* Light theme menu items */
[data-theme="light"] :deep(.n-menu .n-menu-item) {
  color: #333333;
}

[data-theme="light"] :deep(.n-menu .n-menu-item:hover) {
  background-color: #f5f5f5;
  transform: translateY(-2px);
}

[data-theme="light"] :deep(.n-menu .n-menu-item.n-menu-item--selected) {
  background: linear-gradient(135deg, #18a058, #36ad6a);
  color: white;
  box-shadow: 0 4px 12px rgba(24, 160, 88, 0.3);
}

/* Dark theme menu items */
[data-theme="dark"] :deep(.n-menu .n-menu-item) {
  color: #ffffff;
}

[data-theme="dark"] :deep(.n-menu .n-menu-item:hover) {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

[data-theme="dark"] :deep(.n-menu .n-menu-item.n-menu-item--selected) {
  background: linear-gradient(135deg, #63e2b7, #36ad6a);
  color: #1a1a2e;
  box-shadow: 0 4px 12px rgba(99, 226, 183, 0.3);
}

/* Button theming */
[data-theme="light"] :deep(.n-button) {
  color: #333333;
}

[data-theme="dark"] :deep(.n-button) {
  color: #ffffff;
}

/* Drawer content RTL support */
.rtl-content :deep(.n-drawer-header) {
  direction: rtl;
  text-align: right;
}

.rtl-content :deep(.n-drawer-body) {
  direction: rtl;
}

/* Tooltip and Dropdown RTL positioning */
.rtl-layout :deep(.n-tooltip),
.rtl-layout :deep(.n-dropdown) {
  direction: rtl;
}

/* Force dropdown content alignment for RTL */
.rtl-layout :deep(.n-dropdown-menu) {
  direction: rtl;
  text-align: right;
}

.rtl-layout :deep(.n-dropdown-option) {
  direction: rtl;
  justify-content: flex-end;
}

/* Ensure tooltips don't extend beyond viewport */
:deep(.n-tooltip-content) {
  max-width: 200px;
  word-wrap: break-word;
}

/* RTL specific dropdown positioning overrides */
.rtl-layout .navbar-actions :deep(.n-dropdown),
.rtl-layout .navbar-actions :deep(.n-tooltip) {
  position: relative;
}

.rtl-layout .navbar-actions .action-btn:deep(.n-dropdown),
.rtl-layout .navbar-actions .action-btn:deep(.n-tooltip) {
  transform-origin: left center;
}

/* Force RTL dropdown positioning - center all dropdowns under their triggers */
.navbar-actions .action-btn :deep(.n-dropdown-menu),
.navbar-actions .rtl-dropdown :deep(.n-dropdown-menu) {
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
  transform-origin: top center !important;
}

/* Apply to all action buttons regardless of RTL/LTR */
.navbar-actions :deep(.n-dropdown-menu) {
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
  transform-origin: top center !important;
}

/* Override for RTL layout specifically */
.rtl-layout .navbar-actions :deep(.n-dropdown-menu) {
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
  transform-origin: top center !important;
}

/* Mobile menu RTL support */
.rtl-layout :deep(.n-menu .n-menu-item) {
  direction: rtl;
  text-align: right;
}

.rtl-layout :deep(.n-menu .n-menu-item .n-menu-item-content) {
  direction: rtl;
  justify-content: flex-end;
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Focus styles for accessibility */
.action-btn:focus-visible,
.mobile-action-item:focus-visible,
.language-option:focus-visible {
  outline: 2px solid #18a058;
  outline-offset: 2px;
}

[data-theme="dark"] .action-btn:focus-visible,
[data-theme="dark"] .mobile-action-item:focus-visible,
[data-theme="dark"] .language-option:focus-visible {
  outline-color: #63e2b7;
}

/* ===== CUSTOM USER POPOVER STYLES ===== */
.user-avatar-container {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.custom-user-popover {
  /* Position and z-index are handled by inline styles */
  box-sizing: border-box;
  contain: layout;
  min-width: 180px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08);
}

.user-dropdown {
  padding: 8px;
  background: transparent; /* Background handled by inline styles */
  border-radius: 8px;
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: transparent;
  min-height: 44px; /* Good touch target */
}

/* Light theme user menu items */
[data-theme='light'] .user-menu-item {
  color: #2d3748;
}

[data-theme='light'] .user-menu-item:hover {
  background: rgba(45, 55, 72, 0.08);
  transform: translateX(2px);
}

[data-theme='light'] .user-menu-item--logout {
  color: #e53e3e;
}

[data-theme='light'] .user-menu-item--logout:hover {
  background: rgba(229, 62, 62, 0.1);
  color: #e53e3e;
}

[data-theme='light'] .user-menu-divider {
  background: rgba(0, 0, 0, 0.15);
}

/* Dark theme user menu items */
[data-theme='dark'] .user-menu-item {
  color: #e2e8f0;
}

[data-theme='dark'] .user-menu-item:hover {
  background: rgba(226, 232, 240, 0.1);
  transform: translateX(2px);
}

[data-theme='dark'] .user-menu-item--logout {
  color: #fc8181;
}

[data-theme='dark'] .user-menu-item--logout:hover {
  background: rgba(252, 129, 129, 0.15);
  color: #fc8181;
}

[data-theme='dark'] .user-menu-divider {
  background: rgba(255, 255, 255, 0.2);
}

.menu-item-icon {
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.menu-item-text {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
  transition: all 0.3s ease;
}

.user-menu-divider {
  height: 1px;
  margin: 8px 0;
  opacity: 0.6;
}

/* RTL Support for User Menu */
.rtl .user-dropdown {
  direction: rtl;
}

.rtl .user-menu-item {
  direction: rtl;
  flex-direction: row-reverse;
}

.rtl .user-menu-item:hover {
  transform: translateX(-2px);
}

.rtl .menu-item-text {
  text-align: right;
}

/* Enhanced shadow for RTL layouts */
.rtl .custom-user-popover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), -2px 4px 8px rgba(0, 0, 0, 0.08);
}

/* Mobile-specific user popover enhancements */
@media (max-width: 768px) {
  .custom-user-popover {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15), 0 6px 12px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
  }

  .rtl .custom-user-popover {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15), -3px 6px 12px rgba(0, 0, 0, 0.1);
  }
  
  .user-menu-item {
    padding: 14px 18px;
    min-height: 48px; /* Larger touch target on mobile */
  }
  
  .menu-item-text {
    font-size: 15px; /* Slightly larger on mobile */
  }
}

/* Theme transitions for user menu */
.user-dropdown,
.user-menu-item,
.menu-item-icon,
.menu-item-text {
  transition: background-color 0.3s ease, 
              border-color 0.3s ease, 
              color 0.3s ease,
              transform 0.3s ease;
}

/* Dark mode specific adjustments */
[data-theme='dark'] .user-menu-item--logout {
  color: #e88080;
}

[data-theme='dark'] .user-menu-item--logout:hover {
  background: rgba(232, 128, 128, 0.1);
  color: #e88080;
}


</style>
