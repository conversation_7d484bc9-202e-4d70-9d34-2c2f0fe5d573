import { promises as fs } from 'fs';
import { createReadStream } from 'fs';
import { createInterface } from 'readline';
import path from 'path';
import type { ClientReportPayload } from '../types/schemas/debugSchemas';
import { LogRotationService } from './logRotationService';

/**
 * Parsed report data structure for dashboard display
 */
export interface ParsedReport {
  reportId: string;
  timestamp: string;
  serverReceivedAt: string;
  clientTimestamp: string;
  sessionId: string;
  userAgent?: string;
  currentUrl?: string;
  userNotes?: string | null;
  reportType?: string;
  reportSeverity?: string;
  reportTitle?: string;
  reportDescription?: string;
  stepsToReproduce?: string;
  expectedBehavior?: string;
  actualBehavior?: string;
  tags?: string[];
  hasTags?: boolean;
  logCount: number;
  logs: any[];
}

/**
 * Options for filtering and pagination
 */
export interface GetReportsOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filterByType?: string;
  filterBySeverity?: string;
  filterByDateStart?: string;
  filterByDateEnd?: string;
  searchQuery?: string;
}

/**
 * Paginated response for reports
 */
export interface GetReportsResponse {
  reports: ParsedReport[];
  total: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Service for handling client-side debug reports
 * 
 * Log Directory Configuration:
 * 1. Constructor parameter (highest priority)
 * 2. CLIENT_LOG_DIRECTORY environment variable
 * 3. Environment-specific defaults:
 *    - Development: ./logs (relative to working directory)
 *    - Production: /var/log/munygo (Unix) or %TEMP%/munygo-logs (Windows)
 */
export class ClientLogService {
  private readonly logDirectory: string;
  private readonly logFileName: string;
  private readonly logRotationService: LogRotationService;

  constructor(logDirectory?: string) {
    // Use provided directory, environment variable, or safe default
    this.logDirectory = logDirectory || 
                       process.env.CLIENT_LOG_DIRECTORY || 
                       this.getDefaultLogDirectory();
    this.logFileName = 'client-reports.log';
    this.logRotationService = new LogRotationService(this.logDirectory);
  }

  /**
   * Get a safe default log directory based on environment
   */
  private getDefaultLogDirectory(): string {
    const isProduction = process.env.NODE_ENV === 'production';
    
    if (isProduction) {
      // In production, use a more secure path
      // Prefer /var/log for Unix-like systems, or a temp directory as fallback
      if (process.platform === 'win32') {
        return path.join(process.env.TEMP || 'C:\\temp', 'munygo-logs');
      } else {
        return '/var/log/munygo';
      }
    } else {
      // In development, use the current working directory
      return path.join(process.cwd(), 'logs');
    }
  }
  /**
   * Save a client debug report
   */
  async saveReport(reportData: ClientReportPayload): Promise<string> {
    try {
      // Generate a unique report ID
      const reportId = `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Prepare the log entry with metadata
      const logEntry = {
        reportId,
        timestamp: new Date().toISOString(),
        serverReceivedAt: new Date().toISOString(),
        clientTimestamp: reportData.timestamp,
        sessionId: reportData.sessionId,
        userAgent: reportData.reportDetails.userContext?.userAgent,
        currentUrl: reportData.reportDetails.userContext?.currentPage,
        userNotes: reportData.reportDetails.additionalNotes || '',
        // Include all report details
        reportType: reportData.reportDetails.type,
        reportSeverity: reportData.reportDetails.severity,
        reportTitle: reportData.reportDetails.title,
        reportDescription: reportData.reportDetails.description,
        stepsToReproduce: reportData.reportDetails.stepsToReproduce,
        expectedBehavior: reportData.reportDetails.expectedBehavior,
        actualBehavior: reportData.reportDetails.actualBehavior,
        tags: reportData.reportDetails.reportTags || reportData.reportDetails.tags || [],
        hasTags: (reportData.reportDetails.reportTags || reportData.reportDetails.tags || []).length > 0,
        logCount: reportData.logs.length,
        logs: reportData.logs,
        // Include the original reportDetails for backward compatibility and detailed access
        reportDetails: reportData.reportDetails,
      };

      // Ensure log directory exists
      await this.ensureLogDirectory();

      // Check if log rotation is needed before writing
      await this.logRotationService.rotateIfNeeded(this.logFileName);

      // Write to file (JSON per line format for easy parsing)
      const logFilePath = path.join(this.logDirectory, this.logFileName);
      const logLine = JSON.stringify(logEntry) + '\n';
      
      await fs.appendFile(logFilePath, logLine, 'utf8');      // Also log to console for immediate visibility during development
      console.log('\n📋 [ClientLogService] New debug report received:');
      console.log(`📋 Report ID: ${reportId}`);
      console.log(`📋 Session ID: ${reportData.sessionId}`);
      console.log(`📋 Log Count: ${reportData.logs.length}`);
      console.log(`📋 User Notes: ${reportData.reportDetails.additionalNotes || 'None'}`);
      console.log(`📋 Current URL: ${reportData.reportDetails.userContext?.currentPage || 'Unknown'}`);
      
      // Log a summary of log levels
      const logLevelCounts = reportData.logs.reduce((acc, log) => {
        acc[log.level] = (acc[log.level] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      console.log(`📋 Log Levels: ${JSON.stringify(logLevelCounts)}`);

      // Show recent errors if any
      const recentErrors = reportData.logs
        .filter(log => log.level === 'ERROR')
        .slice(-3); // Show last 3 errors

      if (recentErrors.length > 0) {
        console.log(`📋 Recent Errors (${recentErrors.length}):`);
        recentErrors.forEach((error, index) => {
          console.log(`📋   ${index + 1}. ${error.message}`);
          if (error.stackTrace) {
            console.log(`📋      Stack: ${error.stackTrace.split('\n')[0]}`);
          }
        });
      }

      console.log(`📋 Full report saved to: ${logFilePath}\n`);

      return reportId;

    } catch (error) {
      console.error('❌ [ClientLogService] Failed to save debug report:', error);
      throw new Error('Failed to save debug report to server');
    }
  }

  /**
   * Ensure the log directory exists
   */
  private async ensureLogDirectory(): Promise<void> {
    try {
      await fs.access(this.logDirectory);
    } catch {
      // Directory doesn't exist, create it
      await fs.mkdir(this.logDirectory, { recursive: true });
      console.log(`📁 [ClientLogService] Created log directory: ${this.logDirectory}`);
    }
  }
  /**
   * Get basic stats about stored reports (for future admin endpoints)
   */
  async getReportStats(): Promise<{ totalReports: number; logFileSize: number }> {
    try {
      const logFilePath = path.join(this.logDirectory, this.logFileName);
      const stats = await fs.stat(logFilePath);
      
      // Count lines in file using streaming approach to avoid memory issues with large files
      const totalReports = await this.countLinesInFile(logFilePath);

      return {
        totalReports,
        logFileSize: stats.size,
      };
    } catch (error) {
      // File doesn't exist yet
      return {
        totalReports: 0,
        logFileSize: 0,
      };
    }
  }

  /**
   * Count non-empty lines in a file using a streaming approach
   */
  private async countLinesInFile(filePath: string): Promise<number> {
    return new Promise((resolve, reject) => {
      let lineCount = 0;
      
      const fileStream = createReadStream(filePath, { encoding: 'utf8' });
      const rl = createInterface({
        input: fileStream,
        crlfDelay: Infinity // To handle Windows line endings properly
      });

      rl.on('line', (line) => {
        if (line.trim()) {
          lineCount++;
        }
      });

      rl.on('close', () => {
        resolve(lineCount);
      });

      rl.on('error', (error) => {
        reject(error);
      });

      fileStream.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Get log directory stats using the rotation service
   */
  async getLogDirectoryStats() {
    return await this.logRotationService.getLogDirectoryStats();
  }

  /**
   * Get the current log directory path (for admin/debug purposes)
   */
  getLogDirectory(): string {
    return this.logDirectory;
  }

  /**
   * Perform log maintenance (rotation and cleanup)
   */
  async performMaintenance(): Promise<void> {
    try {
      await this.logRotationService.rotateIfNeeded(this.logFileName);
      await this.logRotationService.cleanupOldLogs(this.logFileName);
      console.log('📁 [ClientLogService] Log maintenance completed successfully');
    } catch (error) {
      console.error('❌ [ClientLogService] Error during log maintenance:', error);
      throw error;
    }
  }

  /**
   * Parse the log file and return all reports
   */
  async parseLogFile(): Promise<ParsedReport[]> {
    const logFilePath = path.join(this.logDirectory, this.logFileName);
    
    try {
      // Check if file exists
      await fs.access(logFilePath);
    } catch {
      // File doesn't exist yet
      return [];
    }

    return new Promise((resolve, reject) => {
      const reports: ParsedReport[] = [];
      
      const fileStream = createReadStream(logFilePath, { encoding: 'utf8' });
      const rl = createInterface({
        input: fileStream,
        crlfDelay: Infinity
      });

      rl.on('line', (line) => {
        if (line.trim()) {
          try {
            const parsedLine = JSON.parse(line);
            reports.push(parsedLine as ParsedReport);
          } catch (parseError) {
            console.warn('⚠️ [ClientLogService] Failed to parse line:', line.substring(0, 100) + '...');
          }
        }
      });

      rl.on('close', () => {
        resolve(reports);
      });

      rl.on('error', (error) => {
        reject(error);
      });

      fileStream.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Get all reports with filtering, sorting, and pagination
   */
  async getAllReports(options: GetReportsOptions = {}): Promise<GetReportsResponse> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'serverReceivedAt',
      sortOrder = 'desc',
      filterByType,
      filterBySeverity,
      filterByDateStart,
      filterByDateEnd,
      searchQuery
    } = options;

    // Parse all reports from file
    let reports = await this.parseLogFile();

    // Apply filters
    if (filterByType) {
      reports = reports.filter(report => report.reportType === filterByType);
    }

    if (filterBySeverity) {
      reports = reports.filter(report => report.reportSeverity === filterBySeverity);
    }

    if (filterByDateStart) {
      const startDate = new Date(filterByDateStart);
      reports = reports.filter(report => new Date(report.serverReceivedAt) >= startDate);
    }

    if (filterByDateEnd) {
      const endDate = new Date(filterByDateEnd);
      reports = reports.filter(report => new Date(report.serverReceivedAt) <= endDate);
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      reports = reports.filter(report => 
        report.reportTitle?.toLowerCase().includes(query) ||
        report.reportDescription?.toLowerCase().includes(query) ||
        report.userNotes?.toLowerCase().includes(query) ||
        report.reportId.toLowerCase().includes(query) ||
        report.currentUrl?.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    reports.sort((a, b) => {
      let aValue: any = a[sortBy as keyof ParsedReport];
      let bValue: any = b[sortBy as keyof ParsedReport];

      // Handle date sorting
      if (sortBy === 'serverReceivedAt' || sortBy === 'clientTimestamp' || sortBy === 'timestamp') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle string/number comparison
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      }
    });

    // Calculate pagination
    const total = reports.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedReports = reports.slice(startIndex, endIndex);

    return {
      reports: paginatedReports,
      total,
      totalPages,
      currentPage: page
    };
  }

  /**
   * Get a single report by ID
   */
  async getReportById(reportId: string): Promise<ParsedReport | null> {
    const reports = await this.parseLogFile();
    return reports.find(report => report.reportId === reportId) || null;
  }
}
