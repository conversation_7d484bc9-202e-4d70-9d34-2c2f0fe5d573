import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import DebugReportButtonEnhanced from '../DebugReportButtonEnhanced.vue'
import { createI18n } from 'vue-i18n'
import type { ComponentPublicInstance } from 'vue'

// Mock the useClientLogger composable
const mockSendLogsToServer = vi.fn()
const mockClearLogs = vi.fn()
const mockLogger = {
  sendLogsToServer: mockSendLogsToServer,
  clearLogs: mockClearLogs,
  logs: [],
  error: vi.fn(),
  info: vi.fn(),
  warn: vi.fn()
}

vi.mock('../../composables/useClientLogger', () => ({
  useClientLogger: () => mockLogger
}))

// Mock Naive UI icons
vi.mock('@vicons/ionicons5', () => ({
  BugOutline: { template: '<div data-testid="bug-icon">Bug</div>' },
  AlertCircleOutline: { template: '<div data-testid="alert-icon">Alert</div>' },
  LightbulbOutline: { template: '<div data-testid="lightbulb-icon">Lightbulb</div>' },
  ChatbubbleEllipsesOutline: { template: '<div data-testid="chat-icon">Chat</div>' }
}))

// Create i18n instance with test translations
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en: {
      debug: {
        reportIssue: 'Report Issue',
        reportType: 'Report Type',
        title: 'Title',
        titlePlaceholder: 'Brief summary of the issue',
        description: 'Description',
        descriptionPlaceholder: 'Detailed description',
        stepsToReproduce: 'Steps to Reproduce',
        stepsPlaceholder: 'Step-by-step instructions',
        expectedBehavior: 'Expected Behavior',
        expectedPlaceholder: 'What should happen',
        actualBehavior: 'Actual Behavior',
        actualPlaceholder: 'What actually happens',
        severity: 'Severity',
        additionalNotes: 'Additional Notes',
        notesPlaceholder: 'Any other relevant information',
        submitReport: 'Submit Report',
        submitting: 'Submitting...',
        reportSubmitted: 'Report submitted successfully',
        reportFailed: 'Failed to submit report'
      }
    }
  }
})

describe('DebugReportButtonEnhanced', () => {
  let wrapper: VueWrapper<ComponentPublicInstance>
  let originalEnv: string | undefined

  beforeEach(() => {
    // Save original NODE_ENV
    originalEnv = process.env.NODE_ENV
    // Set to development to show the debug button
    process.env.NODE_ENV = 'development'
    
    // Reset all mocks
    vi.clearAllMocks()
    
    wrapper = mount(DebugReportButtonEnhanced, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false
          }),
          i18n
        ]
      }
    })
  })

  afterEach(() => {
    // Restore original NODE_ENV
    process.env.NODE_ENV = originalEnv
    wrapper?.unmount()
  })

  describe('Component Visibility', () => {
    it('should show debug button in development environment', () => {
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      expect(debugButton.exists()).toBe(true)
      expect(debugButton.text()).toContain('Report Issue')
    })

    it('should hide debug button in production environment', async () => {
      process.env.NODE_ENV = 'production'
      
      await wrapper.unmount()
      wrapper = mount(DebugReportButtonEnhanced, {
        global: {
          plugins: [
            createTestingPinia({
              createSpy: vi.fn,
              stubActions: false
            }),
            i18n
          ]
        }
      })

      const debugButton = wrapper.find('[data-testid="nbutton"]')
      expect(debugButton.exists()).toBe(false)
    })
  })

  describe('Modal Interaction', () => {
    it('should open modal when debug button is clicked', async () => {
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      const modal = wrapper.find('[data-testid="nmodal"]')
      expect(modal.exists()).toBe(true)
    })

    it('should close modal when showModal is set to false', async () => {
      // Open modal first
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')

      // Set showModal to false via component data
      await wrapper.setData({ showModal: false })

      const modal = wrapper.find('[data-testid="nmodal"]')
      expect(modal.attributes('show')).toBe('false')
    })
  })

  describe('Form Data Collection', () => {
    beforeEach(async () => {
      // Open the modal for form testing
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
    })

    it('should collect title input correctly', async () => {
      const titleInput = wrapper.find('input[placeholder="Brief summary of the issue"]')
      await titleInput.setValue('Test Bug Title')

      const vm = wrapper.vm as any
      expect(vm.reportForm.title).toBe('Test Bug Title')
    })

    it('should collect description input correctly', async () => {
      const descriptionInput = wrapper.find('textarea[placeholder="Detailed description"]')
      await descriptionInput.setValue('This is a detailed description of the bug')

      const vm = wrapper.vm as any
      expect(vm.reportForm.description).toBe('This is a detailed description of the bug')
    })

    it('should collect steps to reproduce for bug reports', async () => {
      // First select bug type
      const vm = wrapper.vm as any
      await wrapper.setData({ 
        reportForm: { 
          ...vm.reportForm, 
          type: 'bug' 
        } 
      })

      const stepsInput = wrapper.find('textarea[placeholder="Step-by-step instructions"]')
      await stepsInput.setValue('1. Navigate to page\n2. Click button\n3. Observe error')

      expect(vm.reportForm.stepsToReproduce).toBe('1. Navigate to page\n2. Click button\n3. Observe error')
    })

    it('should collect expected behavior for bug reports', async () => {
      // First select bug type
      const vm = wrapper.vm as any
      await wrapper.setData({ 
        reportForm: { 
          ...vm.reportForm, 
          type: 'bug' 
        } 
      })

      const expectedInput = wrapper.find('textarea[placeholder="What should happen"]')
      await expectedInput.setValue('Page should load without errors')

      expect(vm.reportForm.expectedBehavior).toBe('Page should load without errors')
    })

    it('should collect actual behavior for bug reports', async () => {
      // First select bug type
      const vm = wrapper.vm as any
      await wrapper.setData({ 
        reportForm: { 
          ...vm.reportForm, 
          type: 'bug' 
        } 
      })

      const actualInput = wrapper.find('textarea[placeholder="What actually happens"]')
      await actualInput.setValue('Page shows 500 error')

      expect(vm.reportForm.actualBehavior).toBe('Page shows 500 error')
    })

    it('should collect additional notes', async () => {
      const notesInput = wrapper.find('textarea[placeholder="Any other relevant information"]')
      await notesInput.setValue('Additional context about the issue')

      const vm = wrapper.vm as any
      expect(vm.reportForm.additionalNotes).toBe('Additional context about the issue')
    })
  })

  describe('Report Type Selection', () => {
    beforeEach(async () => {
      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
    })

    it('should allow selecting different report types', async () => {
      const vm = wrapper.vm as any
      
      // Test bug type selection
      await wrapper.setData({ 
        reportForm: { 
          ...vm.reportForm, 
          type: 'bug' 
        } 
      })
      expect(vm.reportForm.type).toBe('bug')

      // Test feature request selection
      await wrapper.setData({ 
        reportForm: { 
          ...vm.reportForm, 
          type: 'feature' 
        } 
      })
      expect(vm.reportForm.type).toBe('feature')

      // Test general feedback selection
      await wrapper.setData({ 
        reportForm: { 
          ...vm.reportForm, 
          type: 'feedback' 
        } 
      })
      expect(vm.reportForm.type).toBe('feedback')
    })

    it('should show bug-specific fields only for bug reports', async () => {
      const vm = wrapper.vm as any
      
      // Set to bug type
      await wrapper.setData({ 
        reportForm: { 
          ...vm.reportForm, 
          type: 'bug' 
        } 
      })

      // Bug-specific fields should be visible
      expect(wrapper.find('textarea[placeholder="Step-by-step instructions"]').exists()).toBe(true)
      expect(wrapper.find('textarea[placeholder="What should happen"]').exists()).toBe(true)
      expect(wrapper.find('textarea[placeholder="What actually happens"]').exists()).toBe(true)

      // Change to feature type
      await wrapper.setData({ 
        reportForm: { 
          ...vm.reportForm, 
          type: 'feature' 
        } 
      })

      // Bug-specific fields should not be visible
      expect(wrapper.find('textarea[placeholder="Step-by-step instructions"]').exists()).toBe(false)
      expect(wrapper.find('textarea[placeholder="What should happen"]').exists()).toBe(false)
      expect(wrapper.find('textarea[placeholder="What actually happens"]').exists()).toBe(false)
    })
  })

  describe('Severity Selection', () => {
    beforeEach(async () => {
      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
    })

    it('should allow selecting severity levels', async () => {
      const vm = wrapper.vm as any
      
      // Test different severity levels
      const severityLevels = ['low', 'medium', 'high', 'critical']
      
      for (const severity of severityLevels) {
        await wrapper.setData({ 
          reportForm: { 
            ...vm.reportForm, 
            severity 
          } 
        })
        expect(vm.reportForm.severity).toBe(severity)
      }
    })
  })

  describe('Form Submission', () => {
    beforeEach(async () => {
      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
    })

    it('should call sendLogsToServer with complete form data on submission', async () => {
      const vm = wrapper.vm as any
      
      // Fill out the form with comprehensive data
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: 'Test Bug Report',
          description: 'Detailed bug description',
          stepsToReproduce: '1. Step one\n2. Step two\n3. Step three',
          expectedBehavior: 'Should work correctly',
          actualBehavior: 'Shows error instead',
          severity: 'high',
          additionalNotes: 'Additional context information',
          tags: ['urgent', 'ui-bug']
        }
      })

      // Mock successful submission
      mockSendLogsToServer.mockResolvedValueOnce({ success: true })

      // Find and click submit button
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      // Verify sendLogsToServer was called with the complete form data
      expect(mockSendLogsToServer).toHaveBeenCalledWith({
        type: 'bug',
        title: 'Test Bug Report',
        description: 'Detailed bug description',
        stepsToReproduce: '1. Step one\n2. Step two\n3. Step three',
        expectedBehavior: 'Should work correctly',
        actualBehavior: 'Shows error instead',
        severity: 'high',
        additionalNotes: 'Additional context information',
        tags: ['urgent', 'ui-bug']
      })
    })

    it('should show loading state during submission', async () => {
      const vm = wrapper.vm as any
      
      // Mock a delayed response
      mockSendLogsToServer.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ success: true }), 100))
      )

      // Fill minimal required data
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: 'Test Bug',
          description: 'Test description'
        }
      })

      // Start submission
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      const submitPromise = submitButton.trigger('click')

      // Check loading state
      expect(vm.isLoading).toBe(true)
      expect(wrapper.find('[data-testid="nbutton"]').attributes('disabled')).toBeDefined()

      // Wait for submission to complete
      await submitPromise
    })

    it('should handle submission errors gracefully', async () => {
      const vm = wrapper.vm as any
      
      // Mock failed submission
      mockSendLogsToServer.mockRejectedValueOnce(new Error('Network error'))

      // Fill minimal required data
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: 'Test Bug',
          description: 'Test description'
        }
      })

      // Submit form
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      // Verify error handling
      expect(vm.isLoading).toBe(false)
      expect(mockSendLogsToServer).toHaveBeenCalled()
    })

    it('should reset form after successful submission', async () => {
      const vm = wrapper.vm as any
      
      // Fill out form
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: 'Test Bug Report',
          description: 'Test description',
          severity: 'high',
          additionalNotes: 'Additional notes'
        }
      })

      // Mock successful submission
      mockSendLogsToServer.mockResolvedValueOnce({ success: true })

      // Submit form
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      // Verify form is reset
      expect(vm.reportForm.title).toBe('')
      expect(vm.reportForm.description).toBe('')
      expect(vm.reportForm.additionalNotes).toBe('')
      expect(vm.showModal).toBe(false)
    })
  })

  describe('Data Integrity', () => {
    beforeEach(async () => {
      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
    })

    it('should preserve all form data during type switching', async () => {
      const vm = wrapper.vm as any
      
      // Fill common fields
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          title: 'Persistent Title',
          description: 'Persistent Description',
          severity: 'medium',
          additionalNotes: 'Persistent Notes'
        }
      })

      // Switch to different type
      await wrapper.setData({
        reportForm: {
          ...vm.reportForm,
          type: 'feature'
        }
      })

      // Verify common fields are preserved
      expect(vm.reportForm.title).toBe('Persistent Title')
      expect(vm.reportForm.description).toBe('Persistent Description')
      expect(vm.reportForm.severity).toBe('medium')
      expect(vm.reportForm.additionalNotes).toBe('Persistent Notes')
    })

    it('should maintain bug-specific fields when switching back to bug type', async () => {
      const vm = wrapper.vm as any
      
      // Fill bug-specific fields
      await wrapper.setData({
        reportForm: {
          type: 'bug',
          stepsToReproduce: 'Original steps',
          expectedBehavior: 'Original expected',
          actualBehavior: 'Original actual'
        }
      })

      // Switch away from bug
      await wrapper.setData({
        reportForm: {
          ...vm.reportForm,
          type: 'feature'
        }
      })

      // Switch back to bug
      await wrapper.setData({
        reportForm: {
          ...vm.reportForm,
          type: 'bug'
        }
      })

      // Verify bug fields are preserved
      expect(vm.reportForm.stepsToReproduce).toBe('Original steps')
      expect(vm.reportForm.expectedBehavior).toBe('Original expected')
      expect(vm.reportForm.actualBehavior).toBe('Original actual')
    })
  })

  describe('Edge Cases', () => {
    beforeEach(async () => {
      // Open the modal
      const debugButton = wrapper.find('[data-testid="nbutton"]')
      await debugButton.trigger('click')
    })

    it('should handle empty form submission', async () => {
      const vm = wrapper.vm as any
      
      // Submit empty form
      const submitButton = wrapper.find('[data-testid="nbutton"]:not(:first-child)')
      await submitButton.trigger('click')

      // Should still call sendLogsToServer with empty/default data
      expect(mockSendLogsToServer).toHaveBeenCalledWith(vm.reportForm)
    })

    it('should handle very long text inputs', async () => {
      const vm = wrapper.vm as any
      const longText = 'A'.repeat(10000) // Very long text
      
      // Set long text in various fields
      const titleInput = wrapper.find('input[placeholder="Brief summary of the issue"]')
      await titleInput.setValue(longText)

      const descriptionInput = wrapper.find('textarea[placeholder="Detailed description"]')
      await descriptionInput.setValue(longText)

      // Verify data is captured correctly
      expect(vm.reportForm.title).toBe(longText)
      expect(vm.reportForm.description).toBe(longText)
    })

    it('should handle special characters in inputs', async () => {
      const vm = wrapper.vm as any
      const specialText = '!@#$%^&*()_+{}[]|\\:";\'<>?,./ émojis 🐛🔧 unicode ñáéíóú'
      
      const titleInput = wrapper.find('input[placeholder="Brief summary of the issue"]')
      await titleInput.setValue(specialText)

      expect(vm.reportForm.title).toBe(specialText)
    })
  })
})
