<template>
  <section class="report-list-section">
    <div class="list-header">
      <div class="results-info">
        <span v-if="!loading">Showing {{ reports.length }} of {{ totalReports }} reports</span>
        <span v-else>Loading reports...</span>
      </div>
      <div class="view-toggle">
        <button 
          @click="currentViewMode = 'table'" 
          :class="{ active: currentViewMode === 'table' }" 
          class="btn toggle-btn"
        >
          Table
        </button>
        <button 
          @click="currentViewMode = 'cards'" 
          :class="{ active: currentViewMode === 'cards' }" 
          class="btn toggle-btn"
        >
          Cards
        </button>
      </div>
    </div>

    <div v-if="loading && !reports.length" class="loading-state">
      <div class="loading-spinner"></div>
      <p>Loading reports...</p>
    </div>
    <div v-else-if="error" class="error-state">
      <p>Error loading reports: {{ error }}</p>
      <button @click="$emit('refresh-reports')" class="btn btn-primary">Try Again</button>
    </div>
    <div v-else-if="!reports.length" class="empty-state">
      <p>No debug reports found matching your criteria.</p>
    </div>
    <div v-else class="reports-content">
      <DebugReportTable
        v-if="currentViewMode === 'table'"
        :reports="reports"
        :sort-by="sortBy"
        :sort-order="sortOrder"
        @view-report="report => $emit('view-report', report)"
        @download-report="id => $emit('download-report', id)"
        @sort="criteria => $emit('sort', criteria)"
      />
      <DebugReportCardGrid
        v-if="currentViewMode === 'cards'"
        :reports="reports"
        @view-report="report => $emit('view-report', report)"
        @download-report="id => $emit('download-report', id)"
      />
      <DebugReportPagination
        v-if="totalPages > 1"
        :current-page="currentPage"
        :total-pages="totalPages"
        :page-size="pageSize"
        @change-page="page => $emit('change-page', page)"
        @change-page-size="size => $emit('change-page-size', size)"
      />
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import DebugReportTable from './DebugReportTable.vue';
import DebugReportCardGrid from './DebugReportCardGrid.vue';
import DebugReportPagination from './DebugReportPagination.vue';

interface ParsedReport {
  reportId?: string;
  reportType?: string;
  reportSeverity?: string;
  serverReceivedAt?: string;
  reportDescription?: string;
}

interface ReportSortPayload {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

type ViewMode = 'table' | 'cards';

defineOptions({
  name: 'DebugReportList'
});

defineProps<{
  reports: ParsedReport[];
  totalReports: number;
  loading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  pageSize: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}>();

defineEmits<{
  'view-report': [report: ParsedReport];
  'download-report': [id: string];
  'change-page': [page: number];
  'change-page-size': [size: number];
  'sort': [criteria: ReportSortPayload];
  'refresh-reports': [];
}>();

const currentViewMode = ref<ViewMode>('table');
</script>

<style scoped>
.report-list-section {
  background-color: var(--bg-surface, #fff);
  padding: 1rem;
  border-radius: var(--radius-lg, 8px);
  box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0,0,0,0.1));
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-base, #e0e0e0);
  flex-wrap: wrap;
  gap: 1rem;
}

@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    align-items: stretch;
  }
}

.results-info {
  font-size: var(--font-size-base, 1rem);
  font-weight: var(--font-weight-medium, 500);
  color: var(--text-primary, #333);
}

.view-toggle {
  display: flex;
  gap: 0;
  border: 1px solid var(--border-base, #e0e0e0);
  border-radius: var(--radius-md, 6px);
  overflow: hidden;
}

.toggle-btn {
  padding: 0.375rem 0.75rem;
  background-color: var(--bg-surface, #fff);
  border: none;
  border-right: 1px solid var(--border-base, #e0e0e0);
  color: var(--text-secondary, #666);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.toggle-btn:last-child {
  border-right: none;
}

.toggle-btn:hover:not(.active) {
  background-color: var(--bg-surface-hover, #f8f9fa);
}

.toggle-btn.active {
  background-color: var(--primary-500, #3b82f6);
  color: white;
}

.loading-state, 
.error-state, 
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary, #666);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-base, #e0e0e0);
  border-top: 3px solid var(--primary-500, #3b82f6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state p {
  color: var(--error-500, #ef4444);
  margin-bottom: 1rem;
  font-weight: var(--font-weight-medium, 500);
}

.empty-state p {
  font-size: var(--font-size-lg, 1.125rem);
  margin: 0;
}

.reports-content {
  /* Container for the actual reports display */
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md, 6px);
  border: 1px solid transparent;
  cursor: pointer;
  font-weight: var(--font-weight-semibold, 600);
  font-size: var(--font-size-sm, 0.875rem);
  transition: all 0.2s ease-in-out;
}

.btn-primary {
  background-color: var(--primary-500, #3b82f6);
  color: white;
  border-color: var(--primary-500, #3b82f6);
}

.btn-primary:hover {
  background-color: var(--primary-600, #2563eb);
  border-color: var(--primary-600, #2563eb);
}
</style>