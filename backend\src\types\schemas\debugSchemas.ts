import { z } from 'zod';

/**
 * Zod schemas for client-side debug reporting
 */

export const LogEntrySchema = z.object({
  timestamp: z.string().datetime(),
  level: z.enum(['INFO', 'WARN', 'ERROR', 'DEBUG']),
  message: z.string().min(1).max(1000),
  context: z.record(z.string(), z.any()).optional(),
  url: z.string().url().optional(),
  stackTrace: z.string().optional(),
});

export const UserActionSchema = z.object({
  action: z.string().min(1).max(100),
  timestamp: z.string().datetime(),
  details: z.record(z.string(), z.any()).optional(),
});

export const UserContextSchema = z.object({
  currentPage: z.string().url(),
  userAgent: z.string().max(500),
  viewport: z.object({
    width: z.number().positive(),
    height: z.number().positive(),
  }),
  timestamp: z.string().datetime(),
  userActions: z.array(UserActionSchema).max(100),
  routeHistory: z.array(z.string().max(500)).max(50),
});

export const ReportDetailsSchema = z.object({
  type: z.enum(['bug', 'feature-request', 'performance', 'ui-ux', 'improvement', 'question', 'other']),
  severity: z.enum(['low', 'medium', 'high', 'critical']),
  title: z.string().min(1).max(200),
  description: z.string().min(1).max(2000),
  stepsToReproduce: z.string().max(2000).optional(),
  expectedBehavior: z.string().max(1000).optional(),
  actualBehavior: z.string().max(1000).optional(),
  additionalNotes: z.string().max(1000).optional(),
  // Enhanced correlation fields
  userContext: UserContextSchema.optional(),
  correlatedLogEntries: z.array(LogEntrySchema).max(100).optional(),
  reportTags: z.array(z.string().max(50)).max(20).optional(),
});

export const ClientReportPayloadSchema = z.object({
  logs: z.array(LogEntrySchema).max(500), // Limit to prevent abuse
  reportDetails: ReportDetailsSchema,
  timestamp: z.string().datetime(),
  sessionId: z.string().max(100).optional(),
});

export const ClientReportResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  reportId: z.string().optional(),
});

export type LogEntry = z.infer<typeof LogEntrySchema>;
export type UserAction = z.infer<typeof UserActionSchema>;
export type UserContext = z.infer<typeof UserContextSchema>;
export type ReportDetails = z.infer<typeof ReportDetailsSchema>;
export type ClientReportPayload = z.infer<typeof ClientReportPayloadSchema>;
export type ClientReportResponse = z.infer<typeof ClientReportResponseSchema>;
