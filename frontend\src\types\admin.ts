/**
 * TypeScript type definitions for admin debug dashboard
 */

// Log entry structure
export interface LogEntry {
  timestamp: string;
  level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG';
  message: string;
  context?: Record<string, any>;
  url?: string;
  stackTrace?: string;
}

// Parsed report structure (matches backend ParsedReport)
export interface ParsedReport {
  reportId: string;
  timestamp: string;
  serverReceivedAt: string;
  clientTimestamp: string;
  sessionId: string;
  userAgent?: string;
  currentUrl?: string;
  userNotes?: string | null;
  reportType?: string;
  reportSeverity?: string;
  reportTitle?: string;
  reportDescription?: string;
  stepsToReproduce?: string;
  expectedBehavior?: string;
  actualBehavior?: string;
  tags?: string[];
  hasTags?: boolean;
  logCount: number;
  logs: LogEntry[];
}

// API query parameters
export interface GetReportsQuery {
  page?: number;
  limit?: number;
  sortBy?: 'serverReceivedAt' | 'clientTimestamp' | 'reportType' | 'reportSeverity' | 'reportTitle' | 'logCount';
  sortOrder?: 'asc' | 'desc';
  filterByType?: 'bug' | 'feature-request' | 'performance' | 'ui-ux' | 'improvement' | 'question' | 'other';
  filterBySeverity?: 'low' | 'medium' | 'high' | 'critical';
  filterByDateStart?: string;
  filterByDateEnd?: string;
  searchQuery?: string;
}

// API response structures
export interface GetReportsResponse {
  reports: ParsedReport[];
  total: number;
  totalPages: number;
  currentPage: number;
}

// Filter options for UI components
export interface FilterOptions {
  type?: string;
  severity?: string;
  dateStart?: string;
  dateEnd?: string;
  searchQuery?: string;
}

// Sort options for UI components
export interface SortOptions {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// Report type and severity enums for consistent display
export const REPORT_TYPES = {
  bug: 'Bug',
  'feature-request': 'Feature Request',
  performance: 'Performance',
  'ui-ux': 'UI/UX',
  improvement: 'Improvement',
  question: 'Question',
  other: 'Other'
} as const;

export const REPORT_SEVERITIES = {
  low: 'Low',
  medium: 'Medium',
  high: 'High',
  critical: 'Critical'
} as const;

export const LOG_LEVELS = {
  INFO: 'Info',
  WARN: 'Warning',
  ERROR: 'Error',
  DEBUG: 'Debug'
} as const;

// Color mappings for severity indicators
export const SEVERITY_COLORS = {
  low: 'default',
  medium: 'warning',
  high: 'error',
  critical: 'error'
} as const;

// Color mappings for log level indicators
export const LOG_LEVEL_COLORS = {
  INFO: 'info',
  WARN: 'warning',
  ERROR: 'error',
  DEBUG: 'default'
} as const;

// For backward compatibility with dashboard component
export interface DebugReport {
  id: string;
  timestamp: string;
  level: string;
  userId?: string;
  message: string;
  url?: string;
  userAgent?: string;
  stackTrace?: string;
  additionalData?: any;
}
