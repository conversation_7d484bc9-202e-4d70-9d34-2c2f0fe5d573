import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { Hono } from 'hono'
import createDebugRoutes from '../debugRoutes'
import { ClientLogService } from '../../services/clientLogService'
import type { ClientReportPayload } from '../../types/schemas/debugSchemas'

// Mock the ClientLogService
const mockSaveReport = vi.fn()
const mockGetReportStats = vi.fn()
const mockGetLogDirectoryStats = vi.fn()
const mockGetLogDirectory = vi.fn()
const mockPerformMaintenance = vi.fn()

const mockClientLogService = {
  saveReport: mockSaveReport,
  getReportStats: mockGetReportStats,
  getLogDirectoryStats: mockGetLogDirectoryStats,
  getLogDirectory: mockGetLogDirectory,
  performMaintenance: mockPerformMaintenance
} as unknown as ClientLogService

describe('Debug Routes', () => {
  let app: Hono
  
  beforeEach(() => {
    vi.clearAllMocks()
    app = new Hono()
    app.route('/debug', createDebugRoutes(mockClientLogService))
  })

  describe('POST /debug/report-issue', () => {
    it('should accept valid bug report and return success response', async () => {
      const reportId = 'test-report-123'
      mockSaveReport.mockResolvedValueOnce(reportId)

      const validReportData: ClientReportPayload = {
        timestamp: new Date().toISOString(),
        sessionId: 'test-session-123',
        reportDetails: {
          type: 'bug',
          title: 'Test Bug Report',
          description: 'This is a test bug description',
          stepsToReproduce: '1. Open app\n2. Click button\n3. See error',
          expectedBehavior: 'Button should work',
          actualBehavior: 'Button shows error',
          severity: 'high',
          additionalNotes: 'Happens only on Chrome',
          reportTags: ['urgent', 'browser-specific']
        },
        logs: [
          {
            level: 'ERROR',
            message: 'Button click failed',
            timestamp: new Date().toISOString(),
            context: { component: 'TestButton' }
          }
        ]
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validReportData)
      })

      expect(response.status).toBe(201)
      
      const responseBody = await response.json()
      expect(responseBody).toEqual({
        success: true,
        message: 'Debug report received successfully. Thank you for helping us improve!',
        reportId
      })

      expect(mockSaveReport).toHaveBeenCalledWith(validReportData)
    })

    it('should accept valid feature request', async () => {
      const reportId = 'feature-request-456'
      mockSaveReport.mockResolvedValueOnce(reportId)

      const featureRequestData: ClientReportPayload = {
        timestamp: new Date().toISOString(),
        sessionId: 'feature-session-456',
        reportDetails: {
          type: 'feature-request',
          title: 'Dark Mode Support',
          description: 'Please add dark mode to the application',
          severity: 'low',
          additionalNotes: 'Would improve user experience significantly',
          reportTags: ['enhancement', 'ui']
        },
        logs: []
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(featureRequestData)
      })

      expect(response.status).toBe(201)

      const responseBody = await response.json()
      expect(responseBody.success).toBe(true)
      expect(responseBody.reportId).toBe(reportId)

      expect(mockSaveReport).toHaveBeenCalledWith(featureRequestData)
    })

    it('should accept valid feedback report', async () => {
      const reportId = 'feedback-789'
      mockSaveReport.mockResolvedValueOnce(reportId)

      const feedbackData: ClientReportPayload = {
        timestamp: new Date().toISOString(),
        sessionId: 'feedback-session-789',
        reportDetails: {
          type: 'other',
          title: 'General Feedback',
          description: 'The app is great but could be faster',
          severity: 'medium',
          additionalNotes: 'Overall positive experience',
          reportTags: ['performance', 'general']
        },
        logs: []
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(feedbackData)
      })

      expect(response.status).toBe(201)
      expect(mockSaveReport).toHaveBeenCalledWith(feedbackData)
    })

    it('should handle minimal valid report data', async () => {
      const reportId = 'minimal-report-101'
      mockSaveReport.mockResolvedValueOnce(reportId)

      const minimalData: ClientReportPayload = {
        timestamp: new Date().toISOString(),
        reportDetails: {
          type: 'bug',
          title: 'Minimal Bug',
          description: 'Minimal description',
          severity: 'low'
        },
        logs: []
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(minimalData)
      })

      expect(response.status).toBe(201)
      expect(mockSaveReport).toHaveBeenCalledWith(minimalData)
    })

    it('should reject request with missing required fields', async () => {
      const invalidData = {
        reportDetails: {
          type: 'bug',
          // Missing required title and description
          severity: 'low'
        },
        logs: []
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidData)
      })

      expect(response.status).toBe(400)
      expect(mockSaveReport).not.toHaveBeenCalled()
    })

    it('should reject request with invalid report type', async () => {
      const invalidData = {
        reportDetails: {
          type: 'invalid-type',
          title: 'Test Title',
          description: 'Test description',
          severity: 'low'
        },
        logs: []
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidData)
      })

      expect(response.status).toBe(400)
      expect(mockSaveReport).not.toHaveBeenCalled()
    })

    it('should reject request with invalid severity', async () => {
      const invalidData = {
        reportDetails: {
          type: 'bug',
          title: 'Test Title',
          description: 'Test description',
          severity: 'invalid-severity'
        },
        logs: []
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidData)
      })

      expect(response.status).toBe(400)
      expect(mockSaveReport).not.toHaveBeenCalled()
    })

    it('should reject request with invalid log level', async () => {
      const invalidData = {
        reportDetails: {
          type: 'bug',
          title: 'Test Title',
          description: 'Test description',
          severity: 'low'
        },
        logs: [
          {
            level: 'invalid-level',
            message: 'Test message',
            timestamp: new Date().toISOString()
          }
        ]
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidData)
      })

      expect(response.status).toBe(400)
      expect(mockSaveReport).not.toHaveBeenCalled()
    })

    it('should handle service errors gracefully', async () => {
      mockSaveReport.mockRejectedValueOnce(new Error('Database connection failed'))

      const validData: ClientReportPayload = {
        timestamp: new Date().toISOString(),
        reportDetails: {
          type: 'bug',
          title: 'Test Bug',
          description: 'Test description',
          severity: 'low'
        },
        logs: []
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validData)
      })

      expect(response.status).toBe(500)
      
      const responseBody = await response.json()
      expect(responseBody).toEqual({
        success: false,
        message: 'Failed to process debug report. Please try again or contact support.'
      })

      expect(mockSaveReport).toHaveBeenCalledWith(validData)
    })

    it('should reject malformed JSON', async () => {
      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: '{ invalid json'
      })

      expect(response.status).toBe(400)
      expect(mockSaveReport).not.toHaveBeenCalled()
    })

    it('should reject empty request body', async () => {
      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: ''
      })

      expect(response.status).toBe(400)
      expect(mockSaveReport).not.toHaveBeenCalled()
    })

    it('should handle very large report data', async () => {
      const reportId = 'large-report-999'
      mockSaveReport.mockResolvedValueOnce(reportId)

      const largeText = 'A'.repeat(1000) // 1KB of text - more reasonable for testing
      const largeData: ClientReportPayload = {
        timestamp: new Date().toISOString(),
        reportDetails: {
          type: 'bug',
          title: 'Large Bug Report',
          description: largeText,
          stepsToReproduce: largeText,
          expectedBehavior: largeText.substring(0, 1000),
          actualBehavior: largeText.substring(0, 1000),
          severity: 'medium',
          additionalNotes: largeText.substring(0, 500)
        },
        logs: Array.from({ length: 10 }, (_, i) => ({
          level: 'INFO' as const,
          message: `Log entry ${i}: ${largeText.substring(0, 100)}`,
          timestamp: new Date().toISOString(),
          context: { index: i }
        }))
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(largeData)
      })

      expect(response.status).toBe(201)
      expect(mockSaveReport).toHaveBeenCalledWith(largeData)
    })

    it('should handle special characters in report data', async () => {
      const reportId = 'special-chars-report'
      mockSaveReport.mockResolvedValueOnce(reportId)

      const specialText = 'Special chars: émojis 🐛🔧 unicode ñáéíóú quotes "test" \'test\' backslashes \\ newlines\nand\ttabs'
      const specialCharsData: ClientReportPayload = {
        timestamp: new Date().toISOString(),
        reportDetails: {
          type: 'bug',
          title: specialText,
          description: specialText,
          stepsToReproduce: specialText,
          expectedBehavior: specialText,
          actualBehavior: specialText,
          severity: 'low',
          additionalNotes: specialText,
          reportTags: ['special-chars', 'unicode-test']
        },
        logs: [
          {
            level: 'ERROR',
            message: specialText,
            timestamp: new Date().toISOString(),
            context: { data: specialText }
          }
        ]
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(specialCharsData)
      })

      expect(response.status).toBe(200)
      expect(mockSaveReport).toHaveBeenCalledWith(specialCharsData)
    })
  })

  describe('GET /debug/stats', () => {
    it('should return report statistics successfully', async () => {
      const mockReportStats = {
        totalReports: 42,
        logFileSize: 1024
      }
      const mockLogDirStats = {
        totalSize: 2048,
        fileCount: 3
      }
      mockGetReportStats.mockResolvedValueOnce(mockReportStats)
      mockGetLogDirectoryStats.mockResolvedValueOnce(mockLogDirStats)
      mockGetLogDirectory.mockReturnValueOnce('/test/logs')

      const response = await app.request('/debug/stats', {
        method: 'GET'
      })

      expect(response.status).toBe(200)

      const responseBody = await response.json()
      expect(responseBody).toEqual({
        success: true,
        data: {
          reports: mockReportStats,
          storage: {
            ...mockLogDirStats,
            totalSizeMB: 0
          },
          logDirectory: '/test/logs'
        }
      })

      expect(mockGetReportStats).toHaveBeenCalledOnce()
      expect(mockGetLogDirectoryStats).toHaveBeenCalledOnce()
    })

    it('should handle service errors in stats endpoint', async () => {
      mockGetReportStats.mockRejectedValueOnce(new Error('File system error'))

      const response = await app.request('/debug/stats', {
        method: 'GET'
      })

      expect(response.status).toBe(500)

      const responseBody = await response.json()
      expect(responseBody).toEqual({
        success: false,
        message: 'Failed to retrieve report statistics'
      })

      expect(mockGetReportStats).toHaveBeenCalledOnce()
    })
  })

  describe('Method and Content-Type Validation', () => {
    it('should reject GET requests to report-issue endpoint', async () => {
      const response = await app.request('/debug/report-issue', {
        method: 'GET'
      })

      expect(response.status).toBe(405) // Method Not Allowed
      expect(mockSaveReport).not.toHaveBeenCalled()
    })

    it('should reject PUT requests to report-issue endpoint', async () => {
      const response = await app.request('/debug/report-issue', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      })

      expect(response.status).toBe(405) // Method Not Allowed
      expect(mockSaveReport).not.toHaveBeenCalled()
    })

    it('should reject POST requests to stats endpoint', async () => {
      const response = await app.request('/debug/stats', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      })

      expect(response.status).toBe(405) // Method Not Allowed
      expect(mockGetReportStats).not.toHaveBeenCalled()
    })
  })

  describe('Edge Cases', () => {
    it('should handle reports with empty arrays and strings', async () => {
      const reportId = 'empty-arrays-report'
      mockSaveReport.mockResolvedValueOnce(reportId)

      const emptyData: ClientReportPayload = {
        timestamp: new Date().toISOString(),
        reportDetails: {
          type: 'bug',
          title: 'Empty Title Test',
          description: 'Empty Description Test',
          severity: 'low',
          reportTags: []
        },
        logs: []
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(emptyData)
      })

      expect(response.status).toBe(200)
      expect(mockSaveReport).toHaveBeenCalledWith(emptyData)
    })

    it('should handle reports with missing optional fields', async () => {
      const reportId = 'minimal-optional-report'
      mockSaveReport.mockResolvedValueOnce(reportId)

      const minimalData: ClientReportPayload = {
        timestamp: new Date().toISOString(),
        reportDetails: {
          type: 'other',
          title: 'Minimal feedback',
          description: 'Just basic feedback',
          severity: 'low'
          // Missing optional fields: additionalNotes, reportTags, bug-specific fields
        },
        logs: []
      }

      const response = await app.request('/debug/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(minimalData)
      })

      expect(response.status).toBe(200)
      expect(mockSaveReport).toHaveBeenCalledWith(minimalData)
    })
  })
})
